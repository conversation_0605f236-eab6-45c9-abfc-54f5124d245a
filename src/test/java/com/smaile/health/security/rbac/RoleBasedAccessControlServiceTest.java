package com.smaile.health.security.rbac;

import com.smaile.health.security.context.OrganizationPermissionContext;
import com.smaile.health.security.service.AuthenticationContextService;
import com.smaile.health.security.util.SecurityContextUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for RoleBasedAccessControlService.
 * 
 * Test Coverage:
 * - Permission checking methods
 * - Role checking methods
 * - Organization access methods
 * - Access level evaluation
 * - Error handling and fail-safe defaults
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/26
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("RoleBasedAccessControlService Tests")
class RoleBasedAccessControlServiceTest {

    @Mock
    private SecurityContextUtils securityContextUtils;

    @Mock
    private AuthenticationContextService authenticationContextService;

    @InjectMocks
    private RoleBasedAccessControlService rbacService;

    private static final UUID TEST_ORG_ID = UUID.randomUUID();
    private static final String TEST_RESOURCE = "user";
    private static final String TEST_ACTION = "create";
    private static final String TEST_ROLE = "ADMIN";
    private static final String TEST_PERMISSION = "user:create";

    private OrganizationPermissionContext mockContext;

    @BeforeEach
    void setUp() {
        mockContext = OrganizationPermissionContext.builder()
            .organizationId(TEST_ORG_ID)
            .organizationName("Test Organization")
            .permissions(Set.of("user:create", "user:read", "user:update"))
            .roles(Set.of("ADMIN", "USER"))
            .isInherited(false)
            .build();
    }

    @Test
    @DisplayName("Should return true when user has permission in organization")
    void testHasPermissionInOrganization() {
        // Arrange
        when(securityContextUtils.hasPermissionInOrganization(TEST_ORG_ID, TEST_PERMISSION))
            .thenReturn(true);

        // Act
        boolean result = rbacService.hasPermission(TEST_RESOURCE, TEST_ACTION, TEST_ORG_ID);

        // Assert
        assertTrue(result);
        verify(securityContextUtils).hasPermissionInOrganization(TEST_ORG_ID, TEST_PERMISSION);
    }

    @Test
    @DisplayName("Should return false when user does not have permission in organization")
    void testHasPermissionInOrganizationDenied() {
        // Arrange
        when(securityContextUtils.hasPermissionInOrganization(TEST_ORG_ID, TEST_PERMISSION))
            .thenReturn(false);

        // Act
        boolean result = rbacService.hasPermission(TEST_RESOURCE, TEST_ACTION, TEST_ORG_ID);

        // Assert
        assertFalse(result);
        verify(securityContextUtils).hasPermissionInOrganization(TEST_ORG_ID, TEST_PERMISSION);
    }

    @Test
    @DisplayName("Should check global permission when organization ID is null")
    void testHasPermissionGlobal() {
        // Arrange
        when(securityContextUtils.hasPermission(TEST_PERMISSION)).thenReturn(true);

        // Act
        boolean result = rbacService.hasPermission(TEST_RESOURCE, TEST_ACTION, null);

        // Assert
        assertTrue(result);
        verify(securityContextUtils).hasPermission(TEST_PERMISSION);
        verify(securityContextUtils, never()).hasPermissionInOrganization(any(), any());
    }

    @Test
    @DisplayName("Should return false on permission check exception")
    void testHasPermissionWithException() {
        // Arrange
        when(securityContextUtils.hasPermissionInOrganization(TEST_ORG_ID, TEST_PERMISSION))
            .thenThrow(new RuntimeException("Security context error"));

        // Act
        boolean result = rbacService.hasPermission(TEST_RESOURCE, TEST_ACTION, TEST_ORG_ID);

        // Assert
        assertFalse(result); // Fail-safe default
        verify(securityContextUtils).hasPermissionInOrganization(TEST_ORG_ID, TEST_PERMISSION);
    }

    @Test
    @DisplayName("Should return true when user has role in organization")
    void testHasRoleInOrganization() {
        // Arrange
        when(securityContextUtils.hasRoleInOrganization(TEST_ORG_ID, TEST_ROLE)).thenReturn(true);

        // Act
        boolean result = rbacService.hasRole(TEST_ROLE, TEST_ORG_ID);

        // Assert
        assertTrue(result);
        verify(securityContextUtils).hasRoleInOrganization(TEST_ORG_ID, TEST_ROLE);
    }

    @Test
    @DisplayName("Should return false when user does not have role in organization")
    void testHasRoleInOrganizationDenied() {
        // Arrange
        when(securityContextUtils.hasRoleInOrganization(TEST_ORG_ID, TEST_ROLE)).thenReturn(false);

        // Act
        boolean result = rbacService.hasRole(TEST_ROLE, TEST_ORG_ID);

        // Assert
        assertFalse(result);
        verify(securityContextUtils).hasRoleInOrganization(TEST_ORG_ID, TEST_ROLE);
    }

    @Test
    @DisplayName("Should return true when user has any of the specified roles")
    void testHasAnyRole() {
        // Arrange
        Set<String> roles = Set.of("ADMIN", "MANAGER");
        when(securityContextUtils.getOrganizationContext(TEST_ORG_ID))
            .thenReturn(Optional.of(mockContext));

        // Act
        boolean result = rbacService.hasAnyRole(roles, TEST_ORG_ID);

        // Assert
        assertTrue(result); // mockContext has ADMIN role
        verify(securityContextUtils).getOrganizationContext(TEST_ORG_ID);
    }

    @Test
    @DisplayName("Should return false when user has none of the specified roles")
    void testHasAnyRoleDenied() {
        // Arrange
        Set<String> roles = Set.of("MANAGER", "SUPERVISOR");
        when(securityContextUtils.getOrganizationContext(TEST_ORG_ID))
            .thenReturn(Optional.of(mockContext));

        // Act
        boolean result = rbacService.hasAnyRole(roles, TEST_ORG_ID);

        // Assert
        assertFalse(result); // mockContext doesn't have MANAGER or SUPERVISOR
        verify(securityContextUtils).getOrganizationContext(TEST_ORG_ID);
    }

    @Test
    @DisplayName("Should return false when roles set is null or empty")
    void testHasAnyRoleWithEmptySet() {
        // Act & Assert
        assertFalse(rbacService.hasAnyRole(null, TEST_ORG_ID));
        assertFalse(rbacService.hasAnyRole(Set.of(), TEST_ORG_ID));
        
        // Verify no interactions with security context
        verifyNoInteractions(securityContextUtils);
    }

    @Test
    @DisplayName("Should return true when user has all specified roles")
    void testHasAllRoles() {
        // Arrange
        Set<String> roles = Set.of("ADMIN", "USER");
        when(securityContextUtils.getOrganizationContext(TEST_ORG_ID))
            .thenReturn(Optional.of(mockContext));

        // Act
        boolean result = rbacService.hasAllRoles(roles, TEST_ORG_ID);

        // Assert
        assertTrue(result); // mockContext has both ADMIN and USER roles
        verify(securityContextUtils).getOrganizationContext(TEST_ORG_ID);
    }

    @Test
    @DisplayName("Should return false when user is missing some required roles")
    void testHasAllRolesDenied() {
        // Arrange
        Set<String> roles = Set.of("ADMIN", "MANAGER");
        when(securityContextUtils.getOrganizationContext(TEST_ORG_ID))
            .thenReturn(Optional.of(mockContext));

        // Act
        boolean result = rbacService.hasAllRoles(roles, TEST_ORG_ID);

        // Assert
        assertFalse(result); // mockContext doesn't have MANAGER role
        verify(securityContextUtils).getOrganizationContext(TEST_ORG_ID);
    }

    @Test
    @DisplayName("Should return true when roles set is empty for hasAllRoles")
    void testHasAllRolesWithEmptySet() {
        // Act & Assert
        assertTrue(rbacService.hasAllRoles(null, TEST_ORG_ID)); // Empty requirements
        assertTrue(rbacService.hasAllRoles(Set.of(), TEST_ORG_ID)); // Empty requirements
        
        // Verify no interactions with security context
        verifyNoInteractions(securityContextUtils);
    }

    @Test
    @DisplayName("Should return true when user has any of the specified permissions")
    void testHasAnyPermission() {
        // Arrange
        Set<String> permissions = Set.of("user:create", "user:delete");
        when(securityContextUtils.hasAnyPermissionInOrganization(TEST_ORG_ID, permissions))
            .thenReturn(true);

        // Act
        boolean result = rbacService.hasAnyPermission(permissions, TEST_ORG_ID);

        // Assert
        assertTrue(result);
        verify(securityContextUtils).hasAnyPermissionInOrganization(TEST_ORG_ID, permissions);
    }

    @Test
    @DisplayName("Should return true when user has all specified permissions")
    void testHasAllPermissions() {
        // Arrange
        Set<String> permissions = Set.of("user:create", "user:read");
        when(securityContextUtils.hasAllPermissionsInOrganization(TEST_ORG_ID, permissions))
            .thenReturn(true);

        // Act
        boolean result = rbacService.hasAllPermissions(permissions, TEST_ORG_ID);

        // Assert
        assertTrue(result);
        verify(securityContextUtils).hasAllPermissionsInOrganization(TEST_ORG_ID, permissions);
    }

    @Test
    @DisplayName("Should get organizations with specific permission")
    void testGetOrganizationsWithPermission() {
        // Arrange
        Set<UUID> expectedOrgs = Set.of(TEST_ORG_ID, UUID.randomUUID());
        when(securityContextUtils.getOrganizationsWithPermission(TEST_PERMISSION))
            .thenReturn(expectedOrgs);

        // Act
        Set<UUID> result = rbacService.getOrganizationsWithPermission(TEST_RESOURCE, TEST_ACTION);

        // Assert
        assertEquals(expectedOrgs, result);
        verify(securityContextUtils).getOrganizationsWithPermission(TEST_PERMISSION);
    }

    @Test
    @DisplayName("Should get organizations with specific role")
    void testGetOrganizationsWithRole() {
        // Arrange
        Set<UUID> expectedOrgs = Set.of(TEST_ORG_ID, UUID.randomUUID());
        when(securityContextUtils.getOrganizationsWithRole(TEST_ROLE))
            .thenReturn(expectedOrgs);

        // Act
        Set<UUID> result = rbacService.getOrganizationsWithRole(TEST_ROLE);

        // Assert
        assertEquals(expectedOrgs, result);
        verify(securityContextUtils).getOrganizationsWithRole(TEST_ROLE);
    }

    @Test
    @DisplayName("Should check organization access correctly")
    void testCanAccessOrganization() {
        // Arrange
        when(securityContextUtils.canAccessOrganization(TEST_ORG_ID)).thenReturn(true);

        // Act
        boolean result = rbacService.canAccessOrganization(TEST_ORG_ID);

        // Assert
        assertTrue(result);
        verify(securityContextUtils).canAccessOrganization(TEST_ORG_ID);
    }

    @Test
    @DisplayName("Should get access level correctly")
    void testGetAccessLevel() {
        // Arrange
        when(securityContextUtils.getPermissionLevel(TEST_ORG_ID)).thenReturn("ADMIN");

        // Act
        RoleBasedAccessControlService.AccessLevel result = rbacService.getAccessLevel(TEST_ORG_ID);

        // Assert
        assertEquals(RoleBasedAccessControlService.AccessLevel.ADMIN, result);
        verify(securityContextUtils).getPermissionLevel(TEST_ORG_ID);
    }

    @Test
    @DisplayName("Should return NONE access level on exception")
    void testGetAccessLevelWithException() {
        // Arrange
        when(securityContextUtils.getPermissionLevel(TEST_ORG_ID))
            .thenThrow(new RuntimeException("Context error"));

        // Act
        RoleBasedAccessControlService.AccessLevel result = rbacService.getAccessLevel(TEST_ORG_ID);

        // Assert
        assertEquals(RoleBasedAccessControlService.AccessLevel.NONE, result); // Fail-safe default
        verify(securityContextUtils).getPermissionLevel(TEST_ORG_ID);
    }

    @Test
    @DisplayName("Should check super admin status correctly")
    void testIsSuperAdmin() {
        // Arrange
        when(securityContextUtils.isSuperAdmin()).thenReturn(true);

        // Act
        boolean result = rbacService.isSuperAdmin();

        // Assert
        assertTrue(result);
        verify(securityContextUtils).isSuperAdmin();
    }

    @Test
    @DisplayName("Should check administrative access correctly")
    void testHasAdministrativeAccess() {
        // Arrange
        when(securityContextUtils.hasAdministrativeAccess()).thenReturn(true);

        // Act
        boolean result = rbacService.hasAdministrativeAccess();

        // Assert
        assertTrue(result);
        verify(securityContextUtils).hasAdministrativeAccess();
    }

    @Test
    @DisplayName("Should check administrative access in organization correctly")
    void testHasAdministrativeAccessInOrganization() {
        // Arrange
        when(securityContextUtils.hasAdministrativeAccessInOrganization(TEST_ORG_ID))
            .thenReturn(true);

        // Act
        boolean result = rbacService.hasAdministrativeAccessInOrganization(TEST_ORG_ID);

        // Assert
        assertTrue(result);
        verify(securityContextUtils).hasAdministrativeAccessInOrganization(TEST_ORG_ID);
    }

    @Test
    @DisplayName("Should get access summary correctly")
    void testGetAccessSummary() {
        // Arrange
        Map<String, Object> expectedSummary = Map.of(
            "totalOrganizations", 3,
            "totalPermissions", 15,
            "hasAdminAccess", true
        );
        when(securityContextUtils.getAccessSummary()).thenReturn(expectedSummary);

        // Act
        Map<String, Object> result = rbacService.getAccessSummary();

        // Assert
        assertEquals(expectedSummary, result);
        verify(securityContextUtils).getAccessSummary();
    }

    @Test
    @DisplayName("Should return error map when access summary fails")
    void testGetAccessSummaryWithException() {
        // Arrange
        when(securityContextUtils.getAccessSummary())
            .thenThrow(new RuntimeException("Summary error"));

        // Act
        Map<String, Object> result = rbacService.getAccessSummary();

        // Assert
        assertTrue(result.containsKey("error"));
        assertEquals("Unable to retrieve access summary", result.get("error"));
        verify(securityContextUtils).getAccessSummary();
    }

    @Test
    @DisplayName("Should evaluate simple access expression correctly")
    void testEvaluateAccessExpression() {
        // Arrange
        String expression = "user:create";
        Map<String, Object> context = Map.of("organizationId", TEST_ORG_ID);
        when(securityContextUtils.hasPermissionInOrganization(TEST_ORG_ID, "user:create"))
            .thenReturn(true);

        // Act
        boolean result = rbacService.evaluateAccessExpression(expression, context);

        // Assert
        assertTrue(result);
        verify(securityContextUtils).hasPermissionInOrganization(TEST_ORG_ID, "user:create");
    }

    @Test
    @DisplayName("AccessLevel enum should work correctly")
    void testAccessLevelEnum() {
        // Test fromString method
        assertEquals(RoleBasedAccessControlService.AccessLevel.ADMIN, 
                    RoleBasedAccessControlService.AccessLevel.fromString("ADMIN"));
        assertEquals(RoleBasedAccessControlService.AccessLevel.NONE, 
                    RoleBasedAccessControlService.AccessLevel.fromString("INVALID"));
        assertEquals(RoleBasedAccessControlService.AccessLevel.NONE, 
                    RoleBasedAccessControlService.AccessLevel.fromString(null));

        // Test isAtLeast method
        assertTrue(RoleBasedAccessControlService.AccessLevel.SUPER_ADMIN
                  .isAtLeast(RoleBasedAccessControlService.AccessLevel.ADMIN));
        assertTrue(RoleBasedAccessControlService.AccessLevel.ADMIN
                  .isAtLeast(RoleBasedAccessControlService.AccessLevel.WRITE));
        assertFalse(RoleBasedAccessControlService.AccessLevel.READ
                   .isAtLeast(RoleBasedAccessControlService.AccessLevel.ADMIN));
    }
}
