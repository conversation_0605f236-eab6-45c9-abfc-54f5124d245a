package com.smaile.health.service;

import com.smaile.health.model.UserDTO;

import java.util.Optional;
import java.util.UUID;

public interface IdentityProvider {
    String createUser(UserDTO userDto);
    String createUser(String username, String email, String password);
    void updateUserActivationStatus(String email, Boolean activationStatus);
    Optional<UUID> findByEmailAndUsername(String email, String username);
}
