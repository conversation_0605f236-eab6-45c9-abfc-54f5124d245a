package com.smaile.health.service;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.config.IamConfig;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.model.UserDTO;
import jakarta.annotation.PostConstruct;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.OAuth2Constants;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class KeycloakService implements IdentityProvider {
    private final IamConfig iamConfig;
    private Keycloak keycloak;
    private UsersResource usersResource;

    @PostConstruct
    void postConstruct() {
        keycloak = KeycloakBuilder.builder()
                .serverUrl(iamConfig.getEndpoint())
                .realm(iamConfig.getRealm())
                .clientId(iamConfig.getClientId())
                .clientSecret(iamConfig.getClientSecret())
                .grantType(OAuth2Constants.CLIENT_CREDENTIALS)
                .build();
        log.debug(keycloak.serverInfo().getInfo().toString());
        usersResource = keycloak.realm(iamConfig.getRealm()).users();
        log.debug("Keycloak user resource initialized");
    }

    @LogExecution
    @Override
    public String createUser(UserDTO userDto) {
        if (!usersResource.searchByUsername(userDto.getUsername(), true).isEmpty()) {
            throw new SmaileRuntimeException("User with username [%s] existed".formatted(userDto.getUsername()));
        }
        if (!usersResource.searchByEmail(userDto.getEmail(), true).isEmpty()) {
            throw new SmaileRuntimeException("User with email [%s] existed".formatted(userDto.getEmail()));
        }

        UserRepresentation kcUser = new UserRepresentation();
        kcUser.setEmail(userDto.getEmail());
        kcUser.setUsername(userDto.getUsername());
        kcUser.setEnabled(true);

        Response response = usersResource.create(kcUser);
        log.debug(response.getHeaders().toString());

        String createdUserLocation = String.valueOf(response.getHeaders().get("Location"));
        String keycloakUserId = createdUserLocation.substring(createdUserLocation.lastIndexOf('/') + 1, createdUserLocation.length() - 1);
        log.debug("user has been created -> {}, userId = {}", createdUserLocation, keycloakUserId);
        return keycloakUserId;
    }

    @Override
    public String createUser(String username, String email, String password) {
        if (!usersResource.searchByUsername(username, true).isEmpty()) {
            throw new SmaileRuntimeException("User with username [%s] existed".formatted(username));
        }
        if (!usersResource.searchByEmail(email, true).isEmpty()) {
            throw new SmaileRuntimeException("User with email [%s] existed".formatted(email));
        }
        UserRepresentation kcUser = new UserRepresentation();
        kcUser.setEmail(email);
        kcUser.setUsername(username);
        kcUser.setEnabled(true);
        kcUser.setEmailVerified(true);
        CredentialRepresentation passwordCredential = new CredentialRepresentation();
        passwordCredential.setValue(password);
        passwordCredential.setType(CredentialRepresentation.PASSWORD);
        passwordCredential.setTemporary(false);
        kcUser.setCredentials(List.of(passwordCredential));
        try(
                Response response = usersResource.create(kcUser);
        ) {
            log.debug(response.getHeaders().toString());
            String createdUserLocation = String.valueOf(response.getHeaders().get("Location"));
            String keycloakUserId = createdUserLocation.substring(createdUserLocation.lastIndexOf('/') + 1, createdUserLocation.length() - 1);
            log.debug("user has been created -> {}, userId = {}", createdUserLocation, keycloakUserId);
            if (!(response.getStatus() == Response.Status.CREATED.getStatusCode() || response.getStatus() == Response.Status.OK.getStatusCode())) {
                throw new SmaileRuntimeException("Failed to create user in Keycloak, status: %s".formatted(response.getStatus()));
            }
            return keycloakUserId;
        } catch (Exception e) {
            log.error("Failed to create user in Keycloak: {}", e.getMessage());
            throw new SmaileRuntimeException("Failed to create user in Keycloak: %s".formatted(e.getMessage()));
        }
    }

    private Optional<UserRepresentation> findUserByEmail(String email, Boolean activateStatus) {
        List<UserRepresentation> kcUserList = usersResource.searchByEmail(email, true);
        if (kcUserList.isEmpty()) {
            log.debug("Keycloak user with email = {} was not found", email);
            return Optional.empty();
        }

        UserRepresentation kcUser = kcUserList.get(0);
        if (activateStatus != null && !Objects.equals(kcUser.isEnabled(), activateStatus)) {
            log.debug("Keycloak user was found but mismatch. require active = {}, user active status = {}", activateStatus, kcUser.isEnabled());
            return Optional.empty();
        }

        return Optional.of(kcUser);
    }

    @LogExecution
    @Override
    public void updateUserActivationStatus(String email, Boolean activationStatus) {
        UserRepresentation existingUser = findUserByEmail(email, null)
                .orElseThrow(() -> new SmaileRuntimeException("User with email = %s was not found for update".formatted(email)));

        existingUser.setEnabled(activationStatus);

        usersResource.get(existingUser.getId()).update(existingUser);
    }

    @LogExecution
    @Override
    public Optional<UUID> findByEmailAndUsername(String email, String username) {
        List<UserRepresentation> userList = usersResource.searchByEmail(email, true);
        if (!userList.isEmpty()) {
            return Optional.of(UUID.fromString(userList.get(0).getId()));
        }
        userList = usersResource.searchByUsername(username, true);
        if (!userList.isEmpty()) {
            return Optional.of(UUID.fromString(userList.get(0).getId()));
        }
        return Optional.empty();
    }
}
