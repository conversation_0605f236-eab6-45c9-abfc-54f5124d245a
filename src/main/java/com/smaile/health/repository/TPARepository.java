package com.smaile.health.repository;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.domain.TPA;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface TPARepository extends JpaRepository<TPA, UUID>, JpaSpecificationExecutor<TPA> {

    Optional<TPA> findByNameAndStatusNot(String name, OrganizationStatus status);

    Optional<TPA> findByNameAndIdNot(String name, UUID id);

    Optional<TPA> findByIdAndStatusNot(UUID id, OrganizationStatus status);

    @Query("SELECT t FROM TPA t LEFT JOIN FETCH t.linkedOrganizations WHERE t.id = :id AND t.status != :status")
    Optional<TPA> findByIdAndStatusNotWithRelatedOrgs(@Param("id") UUID id, @Param("status") OrganizationStatus status);
}
