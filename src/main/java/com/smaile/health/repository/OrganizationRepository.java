package com.smaile.health.repository;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.domain.Organization;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.UUID;

public interface OrganizationRepository extends JpaRepository<Organization, UUID>, JpaSpecificationExecutor<Organization> {
    Page<Organization> findByParentId(UUID parentId, Pageable pageable);

    @Modifying(flushAutomatically = true, clearAutomatically = true)
    @Query(
    """ 
    UPDATE Organization o SET o.status = :status WHERE o.id = :id AND o.status = :fromStatus
    """)
    int updateStaus(
            @Param("id") UUID id,
            @Param("status") OrganizationStatus status,
            @Param("fromStatus") OrganizationStatus fromStatus
    );

}
