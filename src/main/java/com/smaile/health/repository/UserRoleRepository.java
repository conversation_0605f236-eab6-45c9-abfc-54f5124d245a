package com.smaile.health.repository;

import com.smaile.health.domain.UserRole;
import com.smaile.health.domain.projection.UserOrganizationRoles;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.UUID;

public interface UserRoleRepository extends JpaRepository<UserRole, UUID> {

    @Query("SELECT uo.user.id as userId," +
            " uo.organization.id as organizationId," +
            " uo.organization.name as organizationName," +
            " uo.organization.type as organizationType," +
            " uo.organization.status as organizationStatus," +
            " ur.role.code as roleCode," +
            " ur.role.name as roleName " +
            "FROM UserRole ur " +
            "JOIN ur.userOrganization uo " +
            "WHERE uo.user.id = :userId")
    List<UserOrganizationRoles> findByUserId(@Param("userId") UUID userId);

}
