package com.smaile.health.repository.specification;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.domain.TPA;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class TPASpecification {
    public static Specification<TPA> searchSpecificationWithTypeFilter(String market, String status, String name, String registrationNumber, String adminType, String icId, OrganizationType tpaType) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Market filter
            if (StringUtils.hasText(market)) {
                predicates.add(criteriaBuilder.equal(root.get("market"), market));
            }

            // Status filter
            if (StringUtils.hasText(status)) {
                try {
                    OrganizationStatus organizationStatus = OrganizationStatus.valueOf(status.toUpperCase());
                    predicates.add(criteriaBuilder.equal(root.get("status"), organizationStatus));
                } catch (IllegalArgumentException e) {
                    // If invalid status provided, return no results
                    return criteriaBuilder.disjunction();
                }
            }

            // Name filter (case-insensitive partial match)
            if (StringUtils.hasText(name)) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("name")),
                        "%" + name.toLowerCase() + "%"
                ));
            }

            // Registration number filter (case-insensitive partial match)
            if (StringUtils.hasText(registrationNumber)) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("registrationNumber")),
                        "%" + registrationNumber.toLowerCase() + "%"
                ));
            }

            // Admin type filter - join with user organizations and roles
            if (StringUtils.hasText(adminType)) {
                var userOrgJoin = root.join("userOrganizations", JoinType.LEFT);
                var userRoleJoin = userOrgJoin.join("userRoles", JoinType.LEFT);
                var roleJoin = userRoleJoin.join("role", JoinType.LEFT);

                // Filter by role code (admin type)
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(roleJoin.get("code")),
                        "%" + adminType.toLowerCase() + "%"
                ));

                predicates.add(criteriaBuilder.equal(userOrgJoin.get("status"), "ACTIVE"));

                predicates.add(criteriaBuilder.equal(userRoleJoin.get("status"), "ACTIVE"));
            }

            // IC ID filter - filter by parent organization ID
            if (StringUtils.hasText(icId)) {
                try {
                    UUID icUuid = UUID.fromString(icId);
                    predicates.add(criteriaBuilder.equal(root.get("parent").get("id"), icUuid));
                } catch (IllegalArgumentException e) {
                    // If invalid UUID provided, return no results
                    return criteriaBuilder.disjunction();
                }
            }

            // TPA Type filter based on user role
            if (tpaType != null) {
                predicates.add(criteriaBuilder.equal(root.get("type"), tpaType));
            }

            // Exclude archived by default
            predicates.add(criteriaBuilder.notEqual(root.get("status"), OrganizationStatus.ARCHIVED));

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
