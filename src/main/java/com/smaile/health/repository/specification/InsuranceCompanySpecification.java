package com.smaile.health.repository.specification;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.domain.InsuranceCompany;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.ArrayList;

public class InsuranceCompanySpecification {

    public static Specification<InsuranceCompany> searchSpecification(String market, String status, String name) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();

            if (StringUtils.hasText(market)) {
                predicates.add(criteriaBuilder.equal(root.get("market"), market));
            }

            if (StringUtils.hasText(status)) {
                try {
                    OrganizationStatus statusEnum = OrganizationStatus.fromString(status);
                    predicates.add(criteriaBuilder.equal(root.get("status"), statusEnum));
                } catch (IllegalArgumentException e) {
                    predicates.add(criteriaBuilder.equal(root.get("status"), OrganizationStatus.ACTIVE));
                }
            } else {
                predicates.add(criteriaBuilder.notEqual(root.get("status"), OrganizationStatus.ARCHIVED));
            }

            if (StringUtils.hasText(name)) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(criteriaBuilder.coalesce(root.get("name"), "")),
                        "%" + name.toLowerCase() + "%"
                ));
            }


            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
