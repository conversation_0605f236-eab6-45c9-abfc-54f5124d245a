package com.smaile.health.repository;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.domain.Professional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
public interface ProfessionalRepository extends JpaRepository<Professional, UUID>, JpaSpecificationExecutor<Professional> {

    boolean existsByPrimaryLicenseId(String primaryLicenseId);

    int countByStatusAndParentId(OrganizationStatus status,UUID parentId);
    int countByParentId(UUID parentId);
}
