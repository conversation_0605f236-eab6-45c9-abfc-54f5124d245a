package com.smaile.health.mapper;

import com.smaile.health.domain.Professional;
import com.smaile.health.model.ProfessionalDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProfessionalMapper {

    ProfessionalDTO toDTO(Professional professional);

    Professional toEntity(ProfessionalDTO professionalDTO);

}