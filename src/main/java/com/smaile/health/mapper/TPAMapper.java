package com.smaile.health.mapper;

import com.smaile.health.domain.TPA;
import com.smaile.health.model.TPADTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", uses = {OrganizationMapper.class}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TPAMapper {

    @Mapping(target = "parent", source = "parent")
    TPADTO toDTO(TPA tpa);

    TPA toEntity(TPADTO tpaDTO);

    void updateEntityFromDTO(TPADTO tpaDTO, @MappingTarget TPA tpa);
}
