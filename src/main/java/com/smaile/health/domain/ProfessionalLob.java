package com.smaile.health.domain;

import com.smaile.health.constants.ProfessionalDocumentType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Entity
@Table(name = "professional_lobs")
@Getter
@Setter
public class ProfessionalLob extends BaseEntity {

    @Id
    @Column(nullable = false, updatable = false)
    private UUID id;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "file_type")
    private String fileType;

//    @Lob
    @Column(name = "file_data")
    private String fileData;

    @Column(name = "document_type")
    @Enumerated(EnumType.STRING)
    private ProfessionalDocumentType documentType;

    @ManyToOne()
    @JoinColumn(name = "professional_id", nullable = false)
    private Professional professional;
}
