package com.smaile.health.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "insurance_companies")
@PrimaryKeyJoinColumn(name = "id")
@Getter
@Setter
public class InsuranceCompany extends Organization {

    @Column(name = "market", nullable = false)
    private String market;

    @Column(name = "country", nullable = false)
    private String country;
}
