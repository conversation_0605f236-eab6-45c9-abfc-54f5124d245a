package com.smaile.health.controller;

import com.smaile.health.constants.RoleEnum;
import com.smaile.health.constants.Status;
import com.smaile.health.mapper.UserMapper;
import com.smaile.health.model.UserDTO;
import com.smaile.health.model.request.CreateUserRequest;
import com.smaile.health.model.request.UpdateUserRequest;
import com.smaile.health.model.response.BaseResponse;
import com.smaile.health.model.response.UserResponse;
import com.smaile.health.service.UserService;
import com.smaile.health.util.PageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping(value = "/users", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "User Controller", description = "User management APIs")
public class UserController {

    private final UserService userService;
    private final UserMapper userMapper;

    @GetMapping("/{id}")
    @Operation(summary = "Get user info by id")
    public ResponseEntity<UserResponse> getUser(@PathVariable UUID id) {
        UserDTO userDto = userService.get(id);
        return ResponseEntity.ok(userMapper.toResponse(userDto));
    }

    @GetMapping("/query")
    @Operation(summary = "Find users by criteria")
    public ResponseEntity<PageResponse<UserResponse>> findUser(
            @RequestParam(required = false, value = "organization_id") UUID organizationId,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) RoleEnum role,
            @RequestParam(required = false) Status status,
            @PageableDefault(sort = "lastUpdated", direction = Sort.Direction.DESC) Pageable pageable) {
        Page<UserDTO> userDtoPage = userService.queryUserByCriteria(organizationId, keyword, role, status, pageable);
        return ResponseEntity.ok(new PageResponse<>(userDtoPage.map(userMapper::toResponse)));
    }


    @PostMapping
    @ApiResponse(responseCode = "201")
    @Operation(summary = "Create a new user")
    public ResponseEntity<BaseResponse<UUID>> createUser(@RequestBody @Valid final CreateUserRequest request) {
        UserDTO userDto = userMapper.toDTO(request);
        final UUID createdId = userService.create(request.getOrganizationId(), userDto);
        return new ResponseEntity<>(new BaseResponse<>(createdId), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update user info")
    public ResponseEntity<BaseResponse<UUID>> updateUser(@PathVariable(name = "id") final UUID id,
                                           @RequestBody @Valid final UpdateUserRequest request) {
        UserDTO userDto = userMapper.toDTO(request);
        userService.update(userDto.getOrganizationId(), id, userDto);
        return new ResponseEntity<>(new BaseResponse<>(id), HttpStatus.OK);
    }
}
