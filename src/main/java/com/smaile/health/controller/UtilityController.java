package com.smaile.health.controller;

import com.smaile.health.model.CountryDTO;
import com.smaile.health.model.MarketDTO;
import com.smaile.health.model.MarketSegmentDTO;
import com.smaile.health.model.ProfessionalSpecialtyDTO;
import com.smaile.health.model.response.ListResponse;
import com.smaile.health.service.UtilityService;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/utilities", produces = MediaType.APPLICATION_JSON_VALUE)
@AllArgsConstructor
public class UtilityController {

    private final UtilityService utilityService;

    @GetMapping("/markets")
    public ResponseEntity<ListResponse<MarketDTO>> getMarkets() {
        return ResponseEntity.ok(ListResponse.of(utilityService.getAllMarket()));
    }

    @GetMapping("/countries")
    public ResponseEntity<ListResponse<CountryDTO>> getCountry() {
        return ResponseEntity.ok(ListResponse.of(utilityService.getAllCountry()));
    }

    @GetMapping("/market-segments")
    public ResponseEntity<ListResponse<MarketSegmentDTO>> getMarketSegment() {
        return ResponseEntity.ok(ListResponse.of(utilityService.getAllMarketSegment()));
    }

    @GetMapping("/professional-specialties")
    public ResponseEntity<ListResponse<ProfessionalSpecialtyDTO>> getProfessionalSpecialties() {
        return ResponseEntity.ok(ListResponse.of(utilityService.getAllProfessionalSpecialty()));
    }

}
