package com.smaile.health.controller;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.mapper.MedicalProviderMapper;
import com.smaile.health.mapper.UserMapper;
import com.smaile.health.model.MedicalProviderDTO;
import com.smaile.health.model.UserDTO;
import com.smaile.health.model.request.CreateMedicalProviderRequest;
import com.smaile.health.model.request.UpdateMedicalProviderRequest;
import com.smaile.health.model.response.BaseResponse;
import com.smaile.health.model.response.MedicalProviderResponse;
import com.smaile.health.service.MedicalProviderService;
import com.smaile.health.util.PageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.UUID;

@RestController
@RequestMapping("/medical-providers")
@Tag(name = "Medical Provider Controller", description = "Medical Provider management APIs")
@Slf4j
@RequiredArgsConstructor
public class MedicalProviderController {
    private final MedicalProviderMapper medicalProviderMapper;
    private final UserMapper userMapper;
    private final MedicalProviderService medicalProviderService;

    @PostMapping
    @Operation(summary = "Create medical provider")
    public ResponseEntity<BaseResponse<UUID>> createMedicalProvider(@RequestBody @Valid CreateMedicalProviderRequest request) {
        MedicalProviderDTO medicalProviderDto = medicalProviderMapper.toDTO(request);
        UserDTO adminDto = userMapper.toDTO(request.getAdminUserRequest());
        return new ResponseEntity<>(new BaseResponse<>(
                medicalProviderService.create(request.getParentId(), medicalProviderDto, adminDto)),
                HttpStatus.CREATED
        );
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get medical provider information by id")
    public ResponseEntity<MedicalProviderResponse> getById(@PathVariable UUID id) {
        MedicalProviderDTO medicalProviderDTO = medicalProviderService.getById(id);
        return ResponseEntity.ok(medicalProviderMapper.toResponse(medicalProviderDTO));
    }

    @GetMapping("/query")
    @Operation(summary = "Query medical provider by criteria")
    public ResponseEntity<PageResponse<MedicalProviderDTO>> queryMedicalProvider(
            @RequestParam(required = false, name = "organization_id") UUID orgId,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) OrganizationStatus status,
            @RequestParam(required = false) String market,
            @PageableDefault(size = 20) Pageable pageable) {
        return ResponseEntity.ok(new PageResponse<>(medicalProviderService.query(orgId, keyword, status, market, pageable)));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update medical provider information")
    public ResponseEntity<BaseResponse<UUID>> updateMedicalProvider(@PathVariable(name = "id") UUID id,
                                                      @RequestBody @Valid UpdateMedicalProviderRequest request) {
        if (!Objects.equals(id, request.getId())) {
            throw new SmaileRuntimeException("Mismatch medical provider, path id = [%s], payload id = [%s]".formatted(id, request.getId()));
        }
        medicalProviderService.update(request.getId(), medicalProviderMapper.toDTO(request));
        return ResponseEntity.ok(new BaseResponse<>(request.getId()));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete medical provider")
    public ResponseEntity<Void> deleteMedicalProvider(@PathVariable(name = "id") UUID id) {
        medicalProviderService.delete(id);
        return ResponseEntity.noContent().build();
    }
}
