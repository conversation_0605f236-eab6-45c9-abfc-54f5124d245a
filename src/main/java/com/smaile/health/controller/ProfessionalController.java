package com.smaile.health.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.smaile.health.model.ProfessionalDTO;
import com.smaile.health.model.ProfessionalSummaryDTO;
import com.smaile.health.model.request.Filter;
import com.smaile.health.model.response.BaseResponse;
import com.smaile.health.service.ProfessionalService;
import com.smaile.health.util.PageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(value = "/professionals", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Professional Controller", description = "Professional management APIs")
public class ProfessionalController {

    private final ProfessionalService professionalService;
    private final ObjectMapper objectMapper;

    @GetMapping
    @Operation(summary = "Get list of Professional ")
    public ResponseEntity<PageResponse<ProfessionalDTO>> getAllProfessionalsPaged(
            @Schema(
                    description = "Type of insurance business (e.g. IC, TPA)",
                    example = "Mike Smith",
                    maxLength = 100
            )
            @RequestParam(required = false, name = "search") String search,
            @Schema(
                    description = "Filter query in JSON format",
                    example = """
                            [
                              {
                                "field":"status",
                                "operator":"EQ",
                                "value":"ACTIVE"}
                            ]
                            """,
                    maxLength = 100
            )
            @RequestParam(required = false, name = "filters") String filterQuery,
            @PageableDefault(size = 20, sort = "dateCreated", direction = Sort.Direction.DESC) Pageable pageable) throws JsonProcessingException {
        List<Filter> filters = new ArrayList<>();
        if (StringUtils.hasText(filterQuery)) {
            filters = objectMapper.readValue(filterQuery, objectMapper.getTypeFactory().constructCollectionType(List.class, Filter.class));
        }
        return ResponseEntity.ok(professionalService.query(search, filters, pageable));
    }

    @GetMapping("/summary")
    @Operation(summary = "Get list of user by organization id")
    public ResponseEntity<BaseResponse<ProfessionalSummaryDTO>> summary() {
        return ResponseEntity.ok(BaseResponse.of(professionalService.summary()));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get professional details")
    public ResponseEntity<BaseResponse<ProfessionalDTO>> detail(@PathVariable("id") UUID professionalId) {
        return ResponseEntity.ok(BaseResponse.of(professionalService.detail(professionalId)));
    }

    @PutMapping("/{id}/approve")
    @Operation(summary = "Approve an professional")
    public ResponseEntity<BaseResponse<ProfessionalDTO>> approve(@PathVariable("id") UUID professionalId) {
        return ResponseEntity.ok(BaseResponse.of(professionalService.approve(professionalId)));
    }

    @PutMapping("/{id}/deny")
    @Operation(summary = "Deny an professional")
    public ResponseEntity<BaseResponse<ProfessionalDTO>> deny(@PathVariable("id") UUID professionalId) {
        return ResponseEntity.ok(BaseResponse.of(professionalService.deny(professionalId)));
    }
}
