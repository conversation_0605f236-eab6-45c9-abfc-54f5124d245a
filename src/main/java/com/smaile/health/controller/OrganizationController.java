package com.smaile.health.controller;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.domain.Organization;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.model.OrganizationDTO;
import com.smaile.health.service.OrganizationService;
import com.smaile.health.util.PageResponse;
import com.smaile.health.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping(value = "/organizations", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Organization Controller", description = "Organization management APIs")
public class OrganizationController {

    private final OrganizationService organizationService;

    @GetMapping("/paged")
    @Operation(summary = "Get list of organization by params")
    @LogExecution
    public ResponseEntity<PageResponse<OrganizationDTO>> getAllOrganizationsPaged(
            @PageableDefault(size = 20, sort = "id") Pageable pageable) {
        UUID parentOrgId = SecurityUtils.getActorOrganization()
                .map(Organization::getId)
                .orElseThrow(() -> new SmaileRuntimeException("Cannot find organization for actor"));
        return ResponseEntity.ok(PageResponse.of(organizationService.findByParentId(parentOrgId, pageable)));
    }
}
