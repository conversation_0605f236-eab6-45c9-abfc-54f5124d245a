package com.smaile.health.config;

import com.smaile.health.security.authentication.SmaileAuthenticationFilter;
import com.smaile.health.security.authentication.SmaileAuthenticationProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.HttpStatusEntryPoint;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * Enhanced Web Security Configuration for SMAILE Health platform.
 * <p>
 * This configuration provides:
 * - Custom authentication provider integration
 * - Organization-specific permission evaluation
 * - Method-level security with custom annotations
 * - Comprehensive RBAC support
 * <p>
 * Security Features:
 * - Stateless session management
 * - Custom authentication filter
 * - Organization-aware permission evaluator
 * - Whitelist for public endpoints
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/08/26
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true, securedEnabled = true)
@Profile("!test")
@RequiredArgsConstructor
public class WebSecurityConfig {

    private final SmaileAuthenticationProvider authenticationProvider;
    private final SmaileAuthenticationFilter authenticationFilter;

    public static final String[] WHITELIST = {
            "/v3/api-docs/**",
            "/swagger-ui/**",
            "/utilities/**",
            "/register/**",
            "/actuator/**"
    };

    /**
     * Configures the authentication manager with custom authentication provider.
     *
     * @return AuthenticationManager configured with SmaileAuthenticationProvider
     */
    @Bean
    public AuthenticationManager authenticationManager() {
        return new ProviderManager(authenticationProvider);
    }

    /**
     * Configures the main security filter chain.
     *
     * @param http HttpSecurity configuration
     * @return SecurityFilterChain configured for SMAILE Health
     * @throws Exception if configuration fails
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
                .cors(AbstractHttpConfigurer::disable)
                .csrf(AbstractHttpConfigurer::disable)

                // Configure exception handling
                .exceptionHandling(e -> e
                        .authenticationEntryPoint(new HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED))
                )

                // Add custom authentication filter
                .addFilterBefore(authenticationFilter, UsernamePasswordAuthenticationFilter.class)

                // Configure authorization rules
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers(WHITELIST).permitAll()
                        .requestMatchers("/about-me").authenticated()
                        .anyRequest().authenticated()
                )

                // Configure session management (stateless for API)
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                );

        return http.build();
    }

}
