//package com.smaile.health.config;
//
//import com.smaile.health.service.UserService;
//import jakarta.servlet.FilterChain;
//import jakarta.servlet.ServletException;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.security.core.context.SecurityContextHolder;
//import org.springframework.stereotype.Component;
//import org.springframework.web.filter.OncePerRequestFilter;
//
//import java.io.IOException;
//
//@Component
//@RequiredArgsConstructor
//@Slf4j
//public class AuthenticationFilter extends OncePerRequestFilter {
//
//    private static final String X_FORWARDED_USER = "x-forwarded-smaile-user";
//    private static final String X_FORWARDED_EMAIL = "x-forwarded-email";
//    private static final String X_FORWARDED_PREFERRED_USERNAME = "x-forwarded-preferred-username";
//
//    private final UserService userService;
//
//    @Override
//    public void doFilterInternal(HttpServletRequest servletRequest, HttpServletResponse servletResponse, FilterChain filterChain) throws ServletException, IOException {
//        String uri = servletRequest.getRequestURI();
//        if (checkWhitelist(uri)) {
////            filterChain.doFilter(servletRequest, servletResponse);
//            return;
//        }
//        String kcUserId = servletRequest.getHeader(X_FORWARDED_USER);
//        String kcEmail = servletRequest.getHeader(X_FORWARDED_EMAIL);
//        String kcPreferredUsername = servletRequest.getHeader(X_FORWARDED_PREFERRED_USERNAME);
//        log.info("Processing request for user kcUserId : {} ", kcUserId); // Debugging log: need remove
//        SmaileAuthentication authentication = userService.getAuthenticationByKeycloakId(kcUserId);
//        SecurityContextHolder.getContext().setAuthentication(authentication);
//        filterChain.doFilter(servletRequest, servletResponse);
//    }
//
//    private boolean checkWhitelist(String uri) {
//        for (String path : WebSecurityConfig.WHITELIST) {
//            if (uri.contains(path.replace("/**", ""))) {
//                return true;
//            }
//        }
//        return false;
//    }
//}
