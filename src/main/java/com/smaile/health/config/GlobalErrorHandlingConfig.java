package com.smaile.health.config;

import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.ReferencedException;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.SmaileValidationException;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.model.response.SmaileApiResponse;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.NativeWebRequest;
import org.zalando.problem.Problem;
import org.zalando.problem.ProblemBuilder;
import org.zalando.problem.Status;
import org.zalando.problem.spring.web.advice.ProblemHandling;
import org.zalando.problem.spring.web.advice.security.SecurityAdviceTrait;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@ControllerAdvice
public class GlobalErrorHandlingConfig implements ProblemHandling, SecurityAdviceTrait {

    @ExceptionHandler(SmaileRuntimeException.class)
    public ResponseEntity<SmaileApiResponse<Map<String, String>>> handleSmaileRuntimeException(
            SmaileRuntimeException exception) {

        Map<String, String> errors = new HashMap<>();
        errors.put("general", exception.getMessage());

        SmaileApiResponse<Map<String, String>> response = SmaileApiResponse.error(errors, exception.getMessage());

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    @ExceptionHandler(NotFoundException.class)
    public ResponseEntity<SmaileApiResponse<Map<String, String>>> handleNotFoundException(
            NotFoundException exception) {

        Map<String, String> errors = new HashMap<>();
        errors.put("general", exception.getMessage());

        SmaileApiResponse<Map<String, String>> response = SmaileApiResponse.error(errors, exception.getMessage());

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    @ExceptionHandler(ReferencedException.class)
    public ResponseEntity<Problem> handleReferencedException(
            ReferencedException exception,
            NativeWebRequest request) {
        
        Problem problem = Problem.builder()
                .withType(URI.create("https://smaile.health/problems/referenced-entity"))
                .withTitle("Referenced Entity")
                .withStatus(Status.CONFLICT)
                .withDetail("Cannot delete entity because it is referenced by other entities")
                .with("message", exception.getMessage())
                .build();
                
        return ResponseEntity.status(HttpStatus.CONFLICT).body(problem);
    }

    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<SmaileApiResponse<Map<String, String>>> handleValidationException(
            ValidationException exception,
            NativeWebRequest request) {

        Map<String, String> errors = new HashMap<>();
        errors.put("general", exception.getMessage());

        SmaileApiResponse<Map<String, String>> response = SmaileApiResponse.error(errors, exception.getMessage());

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    @Override
    public ResponseEntity<Problem> handleConstraintViolation(
            ConstraintViolationException exception,
            NativeWebRequest request) {

        Map<String, String> issueMap = exception.getConstraintViolations().stream()
                .collect(Collectors.toMap(violation -> violation.getPropertyPath().toString(), ConstraintViolation::getMessage));
        ProblemBuilder problemBuilder = Problem.builder()
                .withType(URI.create("https://smaile.health/problems/constraint-violation"))
                .withTitle("Constraint Violation")
                .withStatus(Status.BAD_REQUEST)
                .with("errors", issueMap);

        return ResponseEntity.badRequest().body(problemBuilder.build());
    }

    @Override
    public ResponseEntity<Problem> handleMethodArgumentNotValid(
            MethodArgumentNotValidException exception,
            NativeWebRequest request) {
        Map<String, String> issueMap = exception.getFieldErrors().stream()
                .collect(Collectors.toMap(FieldError::getField, DefaultMessageSourceResolvable::getDefaultMessage));

        ProblemBuilder problemBuilder = Problem.builder()
                .withType(URI.create("https://smaile.health/problems/validation-failed"))
                .withTitle("Validation Failed")
                .with("errors", issueMap)
                .withStatus(Status.BAD_REQUEST);

        return ResponseEntity.badRequest().body(problemBuilder.build());
    }

    @ExceptionHandler(SmaileValidationException.class)
    public ResponseEntity<Problem> handleSmaileValidationException(
            SmaileValidationException exception,
            NativeWebRequest request) {

        ProblemBuilder problemBuilder = Problem.builder()
                .withType(URI.create("https://smaile.health/problems/validation-failed"))
                .withTitle("Validation Failed")
                .with("errors", exception.getErrorMap())
                .withStatus(Status.BAD_REQUEST);

        return ResponseEntity.badRequest().body(problemBuilder.build());
    }
}