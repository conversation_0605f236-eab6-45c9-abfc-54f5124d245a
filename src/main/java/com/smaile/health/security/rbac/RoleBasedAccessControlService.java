package com.smaile.health.security.rbac;

import com.smaile.health.security.context.OrganizationPermissionContext;
import com.smaile.health.security.service.AuthenticationContextService;
import com.smaile.health.security.util.SecurityContextUtils;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * Role-Based Access Control (RBAC) Service for SMAILE Health platform.
 * <p>
 * This service provides comprehensive RBAC functionality including:
 * - Permission checking across organizational hierarchies
 * - Role-based authorization decisions
 * - Access control matrix evaluation
 * - Dynamic permission resolution
 * <p>
 * RBAC Model:
 * - Users are assigned roles within specific organizations
 * - Roles contain sets of permissions
 * - Permissions define what actions can be performed on resources
 * - Inheritance follows organizational hierarchy
 * <p>
 * Security Principles:
 * - Principle of Least Privilege: Users get minimum necessary permissions
 * - Defense in Depth: Multiple layers of access control
 * - Fail-Safe Defaults: Deny access when in doubt
 * - Complete Mediation: Check every access attempt
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RoleBasedAccessControlService {

    private final SecurityContextUtils securityContextUtils;
    private final AuthenticationContextService authenticationContextService;

    /**
     * Checks if the current user has permission to perform an action on a resource.
     * 
     * @param resource The resource being accessed (e.g., "user", "organization", "report")
     * @param action The action being performed (e.g., "create", "read", "update", "delete")
     * @param organizationId The organization context (null for global permissions)
     * @return true if access is granted, false otherwise
     */
    public boolean hasPermission(String resource, String action, UUID organizationId) {
        try {
            String permission = formatPermission(resource, action);
            
            if (organizationId != null) {
                return securityContextUtils.hasPermissionInOrganization(organizationId, permission);
            } else {
                return securityContextUtils.hasPermission(permission);
            }
        } catch (Exception e) {
            log.error("Error checking permission {}:{} for organization {}", resource, action, organizationId, e);
            return false;
        }
    }

    /**
     * Checks if the current user has a specific role in an organization.
     * 
     * @param roleCode The role code to check
     * @param organizationId The organization ID
     * @return true if user has the role, false otherwise
     */
    public boolean hasRole(String roleCode, UUID organizationId) {
        try {
            return securityContextUtils.hasRoleInOrganization(organizationId, roleCode);
        } catch (Exception e) {
            log.error("Error checking role {} for organization {}", roleCode, organizationId, e);
            return false;
        }
    }

    /**
     * Checks if the current user has any of the specified roles in an organization.
     * 
     * @param roleCodes Set of role codes to check
     * @param organizationId The organization ID
     * @return true if user has any of the roles, false otherwise
     */
    public boolean hasAnyRole(Set<String> roleCodes, UUID organizationId) {
        if (roleCodes == null || roleCodes.isEmpty()) {
            return false;
        }
        
        try {
            OrganizationPermissionContext context = securityContextUtils.getOrganizationContext(organizationId)
                .orElse(null);
            
            if (context == null) {
                return false;
            }
            
            return roleCodes.stream().anyMatch(context::hasRole);
        } catch (Exception e) {
            log.error("Error checking roles {} for organization {}", roleCodes, organizationId, e);
            return false; // Fail-safe default
        }
    }

    /**
     * Checks if the current user has all the specified roles in an organization.
     * 
     * @param roleCodes Set of role codes to check
     * @param organizationId The organization ID
     * @return true if user has all roles, false otherwise
     */
    public boolean hasAllRoles(Set<String> roleCodes, UUID organizationId) {
        if (roleCodes == null || roleCodes.isEmpty()) {
            return true;
        }
        
        try {
            OrganizationPermissionContext context = securityContextUtils.getOrganizationContext(organizationId)
                .orElse(null);
            
            if (context == null) {
                return false;
            }
            
            return roleCodes.stream().allMatch(context::hasRole);
        } catch (Exception e) {
            log.error("Error checking all roles {} for organization {}", roleCodes, organizationId, e);
            return false;
        }
    }

    /**
     * Checks if the current user has any of the specified permissions in an organization.
     * 
     * @param permissions Set of permissions to check (format: "resource:action")
     * @param organizationId The organization ID
     * @return true if user has any of the permissions, false otherwise
     */
    public boolean hasAnyPermission(Set<String> permissions, UUID organizationId) {
        if (permissions == null || permissions.isEmpty()) {
            return false;
        }
        
        try {
            return securityContextUtils.hasAnyPermissionInOrganization(organizationId, permissions);
        } catch (Exception e) {
            log.error("Error checking permissions {} for organization {}", permissions, organizationId, e);
            return false;
        }
    }

    /**
     * Checks if the current user has all of the specified permissions in an organization.
     * 
     * @param permissions Set of permissions to check (format: "resource:action")
     * @param organizationId The organization ID
     * @return true if user has all permissions, false otherwise
     */
    public boolean hasAllPermissions(Set<String> permissions, UUID organizationId) {
        if (permissions == null || permissions.isEmpty()) {
            return true;
        }
        
        try {
            return securityContextUtils.hasAllPermissionsInOrganization(organizationId, permissions);
        } catch (Exception e) {
            log.error("Error checking all permissions {} for organization {}", permissions, organizationId, e);
            return false;
        }
    }

    /**
     * Gets all organizations where the current user has a specific permission.
     * 
     * @param resource The resource type
     * @param action The action type
     * @return Set of organization IDs where user has the permission
     */
    public Set<UUID> getOrganizationsWithPermission(String resource, String action) {
        try {
            String permission = formatPermission(resource, action);
            return securityContextUtils.getOrganizationsWithPermission(permission);
        } catch (Exception e) {
            log.error("Error getting organizations with permission {}:{}", resource, action, e);
            return Set.of();
        }
    }

    /**
     * Gets all organizations where the current user has a specific role.
     * 
     * @param roleCode The role code
     * @return Set of organization IDs where user has the role
     */
    public Set<UUID> getOrganizationsWithRole(String roleCode) {
        try {
            return securityContextUtils.getOrganizationsWithRole(roleCode);
        } catch (Exception e) {
            log.error("Error getting organizations with role {}", roleCode, e);
            return Set.of();
        }
    }

    /**
     * Checks if the current user can access a specific organization.
     * 
     * @param organizationId The organization ID
     * @return true if user can access the organization, false otherwise
     */
    public boolean canAccessOrganization(UUID organizationId) {
        try {
            return securityContextUtils.canAccessOrganization(organizationId);
        } catch (Exception e) {
            log.error("Error checking organization access for {}", organizationId, e);
            return false;
        }
    }

    /**
     * Gets the user's access level in an organization.
     * 
     * @param organizationId The organization ID
     * @return Access level string (SUPER_ADMIN, ADMIN, MANAGER, WRITE, READ, INHERITED, LIMITED, NONE)
     */
    public AccessLevel getAccessLevel(UUID organizationId) {
        try {
            String level = securityContextUtils.getPermissionLevel(organizationId);
            return AccessLevel.fromString(level);
        } catch (Exception e) {
            log.error("Error getting access level for organization {}", organizationId, e);
            return AccessLevel.NONE;
        }
    }

    /**
     * Checks if the current user is a super administrator.
     * 
     * @return true if user is super admin, false otherwise
     */
    public boolean isSuperAdmin() {
        try {
            return securityContextUtils.isSuperAdmin();
        } catch (Exception e) {
            log.error("Error checking super admin status", e);
            return false; // Fail-safe default
        }
    }

    /**
     * Checks if the current user has administrative privileges in any organization.
     * 
     * @return true if user has admin access somewhere, false otherwise
     */
    public boolean hasAdministrativeAccess() {
        try {
            return securityContextUtils.hasAdministrativeAccess();
        } catch (Exception e) {
            log.error("Error checking administrative access", e);
            return false; // Fail-safe default
        }
    }

    /**
     * Checks if the current user has administrative privileges in a specific organization.
     * 
     * @param organizationId The organization ID
     * @return true if user has admin access in the organization, false otherwise
     */
    public boolean hasAdministrativeAccessInOrganization(UUID organizationId) {
        try {
            return securityContextUtils.hasAdministrativeAccessInOrganization(organizationId);
        } catch (Exception e) {
            log.error("Error checking administrative access for organization {}", organizationId, e);
            return false; // Fail-safe default
        }
    }

    /**
     * Evaluates a complex access control expression.
     * 
     * @param expression The access control expression (e.g., "user:read OR admin:* IN org1,org2")
     * @param context Additional context for evaluation
     * @return true if expression evaluates to true, false otherwise
     */
    public boolean evaluateAccessExpression(String expression, Map<String, Object> context) {
        try {
            // This is a placeholder for complex expression evaluation
            // In a full implementation, this would parse and evaluate complex expressions
            log.debug("Evaluating access expression: {}", expression);
            
            // For now, treat as simple permission check
            if (expression.contains(":")) {
                String[] parts = expression.split(":");
                if (parts.length >= 2) {
                    UUID orgId = context != null ? (UUID) context.get("organizationId") : null;
                    return hasPermission(parts[0], parts[1], orgId);
                }
            }
            
            return false;
        } catch (Exception e) {
            log.error("Error evaluating access expression: {}", expression, e);
            return false; // Fail-safe default
        }
    }

    /**
     * Formats a permission string from resource and action.
     * 
     * @param resource The resource type
     * @param action The action type
     * @return Formatted permission string
     */
    private String formatPermission(String resource, String action) {
        if (resource == null || action == null) {
            throw new IllegalArgumentException("Resource and action cannot be null");
        }
        return resource.toLowerCase() + ":" + action.toLowerCase();
    }

    /**
     * Enumeration of access levels.
     */
    @Getter
    public enum AccessLevel {
        SUPER_ADMIN("SUPER_ADMIN"),
        ADMIN("ADMIN"),
        MANAGER("MANAGER"),
        MANAGE("MANAGE"),
        WRITE("WRITE"),
        READ("READ"),
        INHERITED("INHERITED"),
        LIMITED("LIMITED"),
        NONE("NONE");

        private final String value;

        AccessLevel(String value) {
            this.value = value;
        }

        public static AccessLevel fromString(String value) {
            if (value == null) {
                return NONE;
            }
            
            for (AccessLevel level : AccessLevel.values()) {
                if (level.value.equalsIgnoreCase(value)) {
                    return level;
                }
            }
            
            return NONE;
        }

        public boolean isAtLeast(AccessLevel other) {
            return this.ordinal() <= other.ordinal();
        }
    }
}
