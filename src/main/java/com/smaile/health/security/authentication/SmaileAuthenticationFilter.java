package com.smaile.health.security.authentication;

import com.smaile.health.config.WebSecurityConfig;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * Authentication filter for SMAILE Health platform.
 * <p>
 * Authentication Flow:
 * 1. Extract credentials from request headers (Keycloak integration)
 * 2. Create SmaileAuthenticationToken
 * 3. Delegate to SmaileAuthenticationProvider for authentication
 * 4. Set authenticated context in SecurityContextHolder
 * <p>
 * Security Features:
 * - Request validation and sanitization
 * - Whitelist checking for public endpoints
 * - Proper error handling and logging
 * - Integration with Spring Security authentication flow
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/25
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SmaileAuthenticationFilter extends OncePerRequestFilter {

    private static final String X_FORWARDED_USER = "x-forwarded-smaile-user";
    private static final String X_FORWARDED_EMAIL = "x-forwarded-email";
    private static final String X_FORWARDED_PREFERRED_USERNAME = "x-forwarded-preferred-username";

    private final AuthenticationProvider authenticationProvider;

    /**
     * Performs authentication for each request.
     *
     * @param request     The HTTP request
     * @param response    The HTTP response
     * @param filterChain The filter chain
     * @throws ServletException if servlet processing fails
     * @throws IOException      if I/O processing fails
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {

        String requestUri = request.getRequestURI();
        String method = request.getMethod();

        log.debug("Processing authentication for {} {}", method, requestUri);

        try {
            if (isWhitelisted(requestUri)) {
                log.debug("Request {} is whitelisted, skipping authentication", requestUri);
                filterChain.doFilter(request, response);
                return;
            }

            AuthenticationCredentials credentials = extractCredentials(request);
            validateCredentials(credentials, requestUri);
            SmaileAuthentication authentication = performAuthentication(credentials, request);
            SecurityContextHolder.getContext().setAuthentication(authentication);

            log.debug("Authentication successful for user: {} ({})",
                    credentials.getEmail(), credentials.getKeycloakId());

        } catch (AuthenticationException e) {
            handleAuthenticationFailure(request, response, e);
            return;
        } catch (Exception e) {
            log.error("Unexpected error during authentication for request {} {}", method, requestUri, e);
            handleAuthenticationFailure(request, response,
                    new AuthenticationException("Authentication system error") {
                    });
            return;
        }

        filterChain.doFilter(request, response);
    }

    /**
     * Extracts authentication credentials from request headers.
     *
     * @param request The HTTP request
     * @return Authentication credentials
     */
    private AuthenticationCredentials extractCredentials(HttpServletRequest request) {
        String keycloakId = request.getHeader(X_FORWARDED_USER);
        String email = request.getHeader(X_FORWARDED_EMAIL);
        String preferredUsername = request.getHeader(X_FORWARDED_PREFERRED_USERNAME);

        return AuthenticationCredentials.builder()
                .keycloakId(keycloakId)
                .email(email)
                .preferredUsername(preferredUsername)
                .build();
    }

    /**
     * Validates that required credentials are present.
     *
     * @param credentials The extracted credentials
     * @param requestUri  The request URI for logging
     * @throws AuthenticationException if validation fails
     */
    private void validateCredentials(AuthenticationCredentials credentials, String requestUri)
            throws AuthenticationException {

        if (!StringUtils.hasText(credentials.getKeycloakId())) {
            throw new AuthenticationException("Missing required authentication header: " + X_FORWARDED_USER) {
            };
        }

        log.info("Authentication attempt for user: {} for URI: {}",
                credentials.getKeycloakId(), requestUri);
    }

    /**
     * Performs authentication using the custom authentication provider.
     *
     * @param credentials The authentication credentials
     * @param request     The HTTP request
     * @return Authenticated SmaileAuthentication
     * @throws AuthenticationException if authentication fails
     */
    private SmaileAuthentication performAuthentication(AuthenticationCredentials credentials,
                                                       HttpServletRequest request)
            throws AuthenticationException {

        SmaileAuthenticationToken token = new SmaileAuthenticationToken(
                credentials.getKeycloakId(),
                credentials.getEmail(),
                credentials.getPreferredUsername()
        );
        token.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        Authentication result = authenticationProvider.authenticate(token);
        if (!(result instanceof SmaileAuthentication)) {
            throw new AuthenticationException("Invalid authentication result type") {
            };
        }

        return (SmaileAuthentication) result;
    }

    /**
     * Handles authentication failures.
     *
     * @param request   The HTTP request
     * @param response  The HTTP response
     * @param exception The authentication exception
     * @throws IOException if I/O processing fails
     */
    private void handleAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
                                             AuthenticationException exception) throws IOException {

        SecurityContextHolder.clearContext();

        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json");
        response.getWriter().write("{\"error\":\"Authentication failed\",\"message\":\"" +
                exception.getMessage() + "\"}");

        log.warn("Authentication failed for request {} {}: {}",
                request.getMethod(), request.getRequestURI(), exception.getMessage());
    }

    /**
     * Checks if the request URI is whitelisted (public endpoints).
     *
     * @param uri The request URI
     * @return true if whitelisted, false otherwise
     */
    private boolean isWhitelisted(String uri) {
        if (uri == null) {
            return false;
        }

        for (String path : WebSecurityConfig.WHITELIST) {
            String cleanPath = path.replace("/**", "");
            if (uri.startsWith(cleanPath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Inner class to hold authentication credentials.
     */
    @lombok.Data
    @lombok.Builder
    private static class AuthenticationCredentials {

        private String keycloakId;
        private String email;
        private String preferredUsername;

    }

}
