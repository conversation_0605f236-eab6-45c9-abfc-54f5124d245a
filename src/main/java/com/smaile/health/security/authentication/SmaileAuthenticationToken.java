package com.smaile.health.security.authentication;

import lombok.Getter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.Objects;

/**
 * Custom authentication token for SMAILE Health platform.
 * <p>
 * This token represents an authentication request containing user credentials
 * from Keycloak integration (headers: x-forwarded-smaile-user, x-forwarded-email, etc.).
 * <p>
 * The token is used by SmaileAuthenticationProvider to authenticate users
 * and build comprehensive permission contexts.
 * <p>
 * Security Features:
 * - Immutable credentials after construction
 * - Proper credential clearing after authentication
 * - Integration with Spring Security authentication flow
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/25
 */
@Getter
public class SmaileAuthenticationToken extends AbstractAuthenticationToken {

    private final String keycloakId;
    private final String email;
    private final String preferredUsername;
    private transient Object credentials;

    /**
     * Creates an unauthenticated token with user credentials.
     *
     * @param keycloakId        The Keycloak user ID (primary credential)
     * @param email             The user's email address
     * @param preferredUsername The user's preferred username
     */
    public SmaileAuthenticationToken(String keycloakId, String email, String preferredUsername) {
        super(null);
        this.keycloakId = keycloakId;
        this.email = email;
        this.preferredUsername = preferredUsername;
        this.credentials = keycloakId;
        setAuthenticated(false);
    }

    /**
     * Creates an authenticated token with authorities.
     *
     * @param keycloakId        The Keycloak user ID
     * @param email             The user's email address
     * @param preferredUsername The user's preferred username
     * @param authorities       The granted authorities
     */
    public SmaileAuthenticationToken(String keycloakId, String email, String preferredUsername,
                                     Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.keycloakId = keycloakId;
        this.email = email;
        this.preferredUsername = preferredUsername;
        this.credentials = null;
        setAuthenticated(true);
    }

    /**
     * Returns the credentials (Keycloak ID) used to authenticate the user.
     *
     * @return The Keycloak ID or null if already authenticated
     */
    @Override
    public Object getCredentials() {
        return credentials;
    }

    /**
     * Returns the principal (Keycloak ID) that identifies the user.
     *
     * @return The Keycloak ID
     */
    @Override
    public Object getPrincipal() {
        return keycloakId;
    }

    /**
     * Returns the name of the authenticated user.
     *
     * @return The email address or preferred username
     */
    @Override
    public String getName() {
        return email != null ? email : preferredUsername;
    }

    /**
     * Clears the credentials after successful authentication.
     * This is a security best practice to prevent credential exposure.
     */
    @Override
    public void eraseCredentials() {
        super.eraseCredentials();
        this.credentials = null;
    }

    /**
     * Returns a string representation of the token for logging purposes.
     * Credentials are masked for security.
     *
     * @return String representation
     */
    @Override
    public String toString() {
        return "SmaileAuthenticationToken{" +
                "keycloakId='" + (keycloakId != null ?
                keycloakId.substring(0, Math.min(8, keycloakId.length())) + "..." :
                "null") + '\'' +
                ", email='" + email + '\'' +
                ", preferredUsername='" + preferredUsername + '\'' +
                ", authenticated=" + isAuthenticated() +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof SmaileAuthenticationToken that)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        return Objects.equals(keycloakId, that.keycloakId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), keycloakId);
    }

}
