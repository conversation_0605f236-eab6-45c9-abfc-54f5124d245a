package com.smaile.health.security.authentication;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * Authentication details for SMAILE Health platform.
 * <p>
 * This class stores additional information about the authentication request
 * that may be useful for audit, security monitoring, and user experience.
 * <p>
 * Information Stored:
 * - User identification details from Keycloak
 * - Authentication timing information
 * - Request context information
 * <p>
 * Security Considerations:
 * - Implements Serializable for session storage if needed
 * - Contains no sensitive information (credentials are stored separately)
 * - Provides audit trail information
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmaileAuthenticationDetails implements Serializable {

    @Serial
    private static final long serialVersionUID = -8480967704468832259L;

    /**
     * The Keycloak user ID used for authentication
     */
    private String keycloakId;

    /**
     * The user's email address from Keycloak
     */
    private String email;

    /**
     * The user's preferred username from Keycloak
     */
    private String preferredUsername;

    /**
     * Authentication method used
     */
    @Builder.Default
    private String authenticationMethod = "KEYCLOAK_HEADER";

    /**
     * Whether this was a successful authentication
     */
    @Builder.Default
    private Boolean successful = true;

}
