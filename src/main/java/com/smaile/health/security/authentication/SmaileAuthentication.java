package com.smaile.health.security.authentication;

import com.smaile.health.domain.User;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.util.Assert;

import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.Collection;
import java.util.Collections;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * SMAILE Authentication represents an authenticated user in the SMAILE Health platform.
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/08/26
 */
@Getter
@Builder
@Slf4j
public final class SmaileAuthentication implements Authentication, Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * The authenticated user (principal)
     */
    private final User principal;

    /**
     * User's roles across all organizations
     */
    private final Set<String> roles;

    /**
     * Authentication details containing metadata
     */
    private final SmaileAuthenticationDetails details;

    /**
     * Authentication timestamp
     */
    private final Instant authenticatedAt;

    /**
     * Whether this authentication is valid
     */
    private final boolean authenticated;

    /**
     * Private constructor for Builder pattern.
     * Validates all required fields and ensures immutability.
     */
    private SmaileAuthentication(User principal, Set<String> roles,
                                 SmaileAuthenticationDetails details,
                                 Instant authenticatedAt, boolean authenticated) {
        Assert.notNull(principal, "Principal cannot be null");
        Assert.notNull(authenticatedAt, "Authentication timestamp cannot be null");

        this.principal = principal;
        this.roles = roles != null ? Collections.unmodifiableSet(roles) : Collections.emptySet();
        this.details = details;
        this.authenticatedAt = authenticatedAt;
        this.authenticated = authenticated;

        log.debug("Created SmaileAuthentication for user: {} with {} roles",
                principal.getEmail(), this.roles.size());
    }

    /**
     * Creates an authenticated SmaileAuthentication instance.
     *
     * @param principal The authenticated user
     * @param roles     The user's roles
     * @param details   Authentication details
     * @return Authenticated SmaileAuthentication instance
     */
    public static SmaileAuthentication authenticated(User principal, Set<String> roles,
                                                     SmaileAuthenticationDetails details) {
        Assert.notNull(principal, "Principal cannot be null for authenticated instance");

        return SmaileAuthentication.builder()
                .principal(principal)
                .roles(roles)
                .details(details)
                .authenticatedAt(Instant.now())
                .authenticated(true)
                .build();
    }

    /**
     * Creates an unauthenticated SmaileAuthentication instance.
     *
     * @param principal The user attempting authentication
     * @return Unauthenticated SmaileAuthentication instance
     */
    public static SmaileAuthentication unauthenticated(User principal) {
        Assert.notNull(principal, "Principal cannot be null");

        return SmaileAuthentication.builder()
                .principal(principal)
                .roles(Collections.emptySet())
                .details(null)
                .authenticatedAt(Instant.now())
                .authenticated(false)
                .build();
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return roles.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toUnmodifiableSet());
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getDetails() {
        return details;
    }

    @Override
    public Object getPrincipal() {
        return principal;
    }

    @Override
    public boolean isAuthenticated() {
        return authenticated;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        if (isAuthenticated) {
            throw new IllegalArgumentException(
                    "Cannot set authentication to true - use authenticated() factory method instead");
        }
        log.debug("Authentication invalidated for user: {}", getName());
    }

    @Override
    public String getName() {
        return principal != null ? principal.getEmail() : "unknown";
    }

    /**
     * Checks if this authentication has expired.
     *
     * @param sessionTimeoutMinutes Session timeout in minutes
     * @return true if authentication has expired
     */
    public boolean isExpired(long sessionTimeoutMinutes) {
        if (!authenticated) {
            return true;
        }

        Instant expirationTime = authenticatedAt.plusSeconds(sessionTimeoutMinutes * 60);
        return Instant.now().isAfter(expirationTime);
    }

    /**
     * Gets the user's unique identifier.
     *
     * @return User's Keycloak ID or null if not available
     */
    public String getUserId() {
        return principal != null ? principal.getKeycloakId() : null;
    }

    /**
     * Gets the user's email address.
     *
     * @return User's email or null if not available
     */
    public String getEmail() {
        return principal != null ? principal.getEmail() : null;
    }

    /**
     * Checks if the user has a specific role.
     *
     * @param role The role to check
     * @return true if user has the role
     */
    public boolean hasRole(String role) {
        return roles.contains(role);
    }

    /**
     * Checks if the user has any of the specified roles.
     *
     * @param rolesToCheck Set of roles to check
     * @return true if user has any of the roles
     */
    public boolean hasAnyRole(Set<String> rolesToCheck) {
        return rolesToCheck != null && roles.stream().anyMatch(rolesToCheck::contains);
    }

    /**
     * Gets an immutable copy of all user roles.
     *
     * @return Immutable set of user roles
     */
    public Set<String> getAllRoles() {
        return Collections.unmodifiableSet(roles);
    }

    // ========== Object Methods ==========

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }

        SmaileAuthentication that = (SmaileAuthentication) obj;
        return authenticated == that.authenticated &&
                Objects.equals(principal, that.principal) &&
                Objects.equals(roles, that.roles) &&
                Objects.equals(authenticatedAt, that.authenticatedAt);
    }

    @Override
    public int hashCode() {
        return Objects.hash(principal, roles, authenticated, authenticatedAt);
    }

    @Override
    public String toString() {
        return String.format("SmaileAuthentication{user='%s', authenticated=%s, roles=%d, timestamp=%s}",
                getEmail(), authenticated, roles.size(), authenticatedAt);
    }

    /**
     * Erases any sensitive credentials.
     * Since this implementation never stores credentials, this is a no-op.
     */
    public void eraseCredentials() {
        // No credentials to erase - they are never stored
        log.debug("eraseCredentials() called for user: {}", getName());
    }

    // ========== Builder Class ==========

    /**
     * Custom builder class that enforces validation rules.
     */
    public static class SmaileAuthenticationBuilder {

        public SmaileAuthentication build() {
            // Validation is performed in the constructor
            return new SmaileAuthentication(principal, roles, details, authenticatedAt, authenticated);
        }

    }

}
