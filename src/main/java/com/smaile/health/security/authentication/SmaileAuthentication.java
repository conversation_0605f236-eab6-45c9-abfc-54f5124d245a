package com.smaile.health.security.authentication;

import com.smaile.health.domain.User;
import com.smaile.health.security.context.OrganizationPermissionContext;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

@Getter
@Setter
public class SmaileAuthentication implements Authentication {

    private boolean isAuthenticated = false;

    private transient User actor;
    private Set<String> roles;
    private Map<UUID, Set<String>> organizationToPermissionsMap;
    private SmaileAuthenticationDetails authenticationDetails;
    private transient Map<UUID, OrganizationPermissionContext> organizationPermissionContexts;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        if (roles == null) {
            return Set.of();
        }
        return roles.stream().map(role -> (GrantedAuthority) () -> role).toList();
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getDetails() {
        return null;
    }

    @Override
    public Object getPrincipal() {
        return actor;
    }

    @Override
    public boolean isAuthenticated() {
        return this.isAuthenticated;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        this.isAuthenticated = isAuthenticated;
    }

    @Override
    public String getName() {
        return actor.getFullName();
    }

    public void setAuthenticationDetails(SmaileAuthenticationDetails authenticationDetails) {
        this.authenticationDetails = authenticationDetails;
    }

    /**
     * Check if user has permission in a specific organization
     */
    public boolean hasPermissionInOrganization(UUID organizationId, String permission) {
        OrganizationPermissionContext context = organizationPermissionContexts.get(organizationId);
        return context != null && context.hasPermission(permission);
    }

    /**
     * Check if user has role in a specific organization
     */
    public boolean hasRoleInOrganization(UUID organizationId, String role) {
        OrganizationPermissionContext context = organizationPermissionContexts.get(organizationId);
        return context != null && context.hasRole(role);
    }

    /**
     * Get all organizations where user has any permissions
     */
    public Set<UUID> getAccessibleOrganizations() {
        return organizationPermissionContexts != null ? organizationPermissionContexts.keySet() : Set.of();
    }

    /**
     * Get permission context for a specific organization
     */
    public OrganizationPermissionContext getOrganizationContext(UUID organizationId) {
        return organizationPermissionContexts != null ? organizationPermissionContexts.get(organizationId) : null;
    }

}
