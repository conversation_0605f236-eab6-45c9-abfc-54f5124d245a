package com.smaile.health.security.service;

import com.smaile.health.security.authentication.SmaileAuthentication;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.UUID;

/**
 * Permission evaluator that uses organization-specific permission contexts
 * built during authentication to evaluate permissions.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class OrganizationPermissionEvaluator implements PermissionEvaluator {

    private final AuthenticationContextService authenticationContextService;

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        if (!(authentication instanceof SmaileAuthentication customAuth)) {
            log.warn("Authentication is not SmaileAuthentication type");
            return false;
        }

        try {
            return evaluatePermission(customAuth, targetDomainObject, permission.toString());
        } catch (Exception e) {
            log.error("Error evaluating permission: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean hasPermission(Authentication authentication,
                                 Serializable targetId,
                                 String targetType,
                                 Object permission) {
        if (!(authentication instanceof SmaileAuthentication customAuth)) {
            log.warn("Authentication is not SmaileAuthentication type");
            return false;
        }

        try {
            UUID organizationId = null;
            if (targetId != null) {
                try {
                    organizationId = UUID.fromString(targetId.toString());
                } catch (IllegalArgumentException e) {
                    log.debug("Target ID is not a valid UUID: {}", targetId);
                }
            }

            return evaluatePermissionById(customAuth, organizationId, targetType, permission.toString());
        } catch (Exception e) {
            log.error("Error evaluating permission by ID: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Evaluate permission with domain object context
     */
    private boolean evaluatePermission(SmaileAuthentication authentication,
                                       Object targetDomainObject,
                                       String permission) {
        User user = authentication.getActor();

        // Super admin has access to everything
        if (isSuperAdmin(authentication)) {
            log.debug("Super admin access granted for user: {}", user.getEmail());
            return true;
        }

        // Extract organization ID from target domain object
        UUID organizationId = extractOrganizationId(targetDomainObject);

        if (organizationId != null) {
            // Check permission in specific organization
            return hasPermissionInOrganization(authentication, organizationId, permission);
        } else {
            // Check permission in any organization
            return authenticationContextService.hasPermissionInAnyOrganization(authentication, permission);
        }
    }

    /**
     * Evaluate permission by organization ID
     */
    private boolean evaluatePermissionById(SmaileAuthentication authentication, UUID organizationId,
                                           String targetType, String permission) {
        User user = authentication.getActor();

        // Super admin has access to everything
        if (isSuperAdmin(authentication)) {
            log.debug("Super admin access granted for user: {}", user.getEmail());
            return true;
        }

        if (organizationId != null) {
            return hasPermissionInOrganization(authentication, organizationId, permission);
        } else {
            return authenticationContextService.hasPermissionInAnyOrganization(authentication, permission);
        }
    }

    /**
     * Check if user has permission in specific organization (including inheritance)
     */
    private boolean hasPermissionInOrganization(SmaileAuthentication authentication,
                                                UUID organizationId,
                                                String permission) {
        // Check direct permission in organization
        if (authentication.hasPermissionInOrganization(organizationId, permission)) {
            log.debug("Direct permission '{}' granted in organization {} for user: {}",
                    permission, organizationId, authentication.getActor().getEmail());
            return true;
        }

        // Check inherited permission through organization hierarchy
        if (authenticationContextService.hasPermissionInOrganizationHierarchy(authentication, organizationId,
                permission)) {
            log.debug("Inherited permission '{}' granted in organization {} for user: {}",
                    permission, organizationId, authentication.getActor().getEmail());
            return true;
        }

        log.debug("Permission '{}' denied in organization {} for user: {}",
                permission, organizationId, authentication.getActor().getEmail());
        return false;
    }

    /**
     * Check if user is super admin
     */
    private boolean isSuperAdmin(SmaileAuthentication authentication) {
        return authentication.getRoles() != null &&
                authentication.getRoles().contains(RoleEnum.SUPER_SMAILE_ADMIN.name());
    }

    /**
     * Extract organization ID from domain object
     */
    private UUID extractOrganizationId(Object targetDomainObject) {
        if (targetDomainObject == null) {
            return null;
        }

        if (targetDomainObject instanceof UUID) {
            return (UUID) targetDomainObject;
        }

        if (targetDomainObject instanceof String) {
            try {
                return UUID.fromString((String) targetDomainObject);
            } catch (IllegalArgumentException e) {
                log.debug("Target domain object is not a valid UUID: {}", targetDomainObject);
            }
        }

        if (targetDomainObject instanceof Organization org) {
            return org.getId();
        }

        return null;
    }

}
