package com.smaile.health.security.audit;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Repository for SecurityAuditEvent entities.
 * Provides methods to query audit events with various filters.
 */
@Repository
public interface SecurityAuditEventRepository extends JpaRepository<SecurityAuditEvent, UUID>, 
                                                     JpaSpecificationExecutor<SecurityAuditEvent> {

    /**
     * Find audit events by user ID
     */
    Page<SecurityAuditEvent> findByUserIdOrderByEventTimestampDesc(UUID userId, Pageable pageable);

    /**
     * Find audit events by organization ID
     */
    Page<SecurityAuditEvent> findByOrganizationIdOrderByEventTimestampDesc(UUID organizationId, Pageable pageable);

    /**
     * Find audit events by event type
     */
    Page<SecurityAuditEvent> findByEventTypeOrderByEventTimestampDesc(SecurityEventType eventType, Pageable pageable);

    /**
     * Find audit events by severity levels
     */
    Page<SecurityAuditEvent> findBySeverityInOrderByEventTimestampDesc(List<String> severities, Pageable pageable);

    /**
     * Find audit events within date range
     */
    Page<SecurityAuditEvent> findByEventTimestampBetweenOrderByEventTimestampDesc(
            OffsetDateTime startDate, OffsetDateTime endDate, Pageable pageable);

    /**
     * Find audit events by resource type and ID
     */
    Page<SecurityAuditEvent> findByResourceTypeAndResourceIdOrderByEventTimestampDesc(
            String resourceType, UUID resourceId, Pageable pageable);

    /**
     * Find failed events (success = false)
     */
    Page<SecurityAuditEvent> findBySuccessFalseOrderByEventTimestampDesc(Pageable pageable);

    /**
     * Find events by IP address
     */
    Page<SecurityAuditEvent> findByIpAddressOrderByEventTimestampDesc(String ipAddress, Pageable pageable);

    /**
     * Find events by session ID
     */
    Page<SecurityAuditEvent> findBySessionIdOrderByEventTimestampDesc(String sessionId, Pageable pageable);

    /**
     * Count events by event type within date range
     */
    @Query("SELECT COUNT(e) FROM SecurityAuditEvent e WHERE e.eventType = :eventType " +
           "AND e.eventTimestamp BETWEEN :startDate AND :endDate")
    long countByEventTypeAndDateRange(@Param("eventType") SecurityEventType eventType,
                                     @Param("startDate") OffsetDateTime startDate,
                                     @Param("endDate") OffsetDateTime endDate);

    /**
     * Count failed login attempts for a user within time period
     */
    @Query("SELECT COUNT(e) FROM SecurityAuditEvent e WHERE e.eventType = :eventType " +
           "AND e.userEmail = :userEmail AND e.success = false " +
           "AND e.eventTimestamp >= :since")
    long countFailedLoginAttempts(@Param("eventType") SecurityEventType eventType,
                                 @Param("userEmail") String userEmail,
                                 @Param("since") OffsetDateTime since);

    /**
     * Find recent events for a user
     */
    @Query("SELECT e FROM SecurityAuditEvent e WHERE e.user.id = :userId " +
           "AND e.eventTimestamp >= :since ORDER BY e.eventTimestamp DESC")
    List<SecurityAuditEvent> findRecentEventsForUser(@Param("userId") UUID userId,
                                                     @Param("since") OffsetDateTime since);

    /**
     * Find security concerns (errors and warnings) within date range
     */
    @Query("SELECT e FROM SecurityAuditEvent e WHERE e.severity IN ('ERROR', 'WARN') " +
           "AND e.eventTimestamp BETWEEN :startDate AND :endDate " +
           "ORDER BY e.eventTimestamp DESC")
    Page<SecurityAuditEvent> findSecurityConcernsByDateRange(@Param("startDate") OffsetDateTime startDate,
                                                            @Param("endDate") OffsetDateTime endDate,
                                                            Pageable pageable);

    /**
     * Find events by correlation ID
     */
    Page<SecurityAuditEvent> findByCorrelationIdOrderByEventTimestampDesc(String correlationId, Pageable pageable);

    /**
     * Get event statistics by event type
     */
    @Query("SELECT e.eventType, COUNT(e) FROM SecurityAuditEvent e " +
           "WHERE e.eventTimestamp BETWEEN :startDate AND :endDate " +
           "GROUP BY e.eventType ORDER BY COUNT(e) DESC")
    List<Object[]> getEventStatisticsByType(@Param("startDate") OffsetDateTime startDate,
                                           @Param("endDate") OffsetDateTime endDate);

    /**
     * Get event statistics by severity
     */
    @Query("SELECT e.severity, COUNT(e) FROM SecurityAuditEvent e " +
           "WHERE e.eventTimestamp BETWEEN :startDate AND :endDate " +
           "GROUP BY e.severity ORDER BY COUNT(e) DESC")
    List<Object[]> getEventStatisticsBySeverity(@Param("startDate") OffsetDateTime startDate,
                                               @Param("endDate") OffsetDateTime endDate);

    /**
     * Find events with specific metadata key-value pair
     */
    @Query("SELECT e FROM SecurityAuditEvent e WHERE " +
           "JSON_EXTRACT(e.metadata, :jsonPath) = :value " +
           "ORDER BY e.eventTimestamp DESC")
    Page<SecurityAuditEvent> findByMetadataValue(@Param("jsonPath") String jsonPath,
                                                @Param("value") String value,
                                                Pageable pageable);

    /**
     * Delete old audit events (for cleanup)
     */
    @Query("DELETE FROM SecurityAuditEvent e WHERE e.eventTimestamp < :cutoffDate")
    int deleteOldEvents(@Param("cutoffDate") OffsetDateTime cutoffDate);
}
