package com.smaile.health.security.audit;

import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
import com.smaile.health.util.SecurityUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.OffsetDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Service for managing security audit events.
 * Provides methods to log, query, and analyze security events.
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class SecurityAuditService {

    private final SecurityAuditEventRepository auditEventRepository;

    /**
     * Log a security event asynchronously
     */
    @Async
    public void logSecurityEvent(SecurityEventType eventType, String description) {
        logSecurityEvent(eventType, description, null, null, null, null, true, null);
    }

    /**
     * Log a security event with resource information
     */
    @Async
    public void logSecurityEvent(SecurityEventType eventType, String description, 
                                String resourceType, UUID resourceId, String resourceName) {
        logSecurityEvent(eventType, description, resourceType, resourceId, resourceName, null, true, null);
    }

    /**
     * Log a security event with full context
     */
    @Async
    public void logSecurityEvent(SecurityEventType eventType, String description,
                                String resourceType, UUID resourceId, String resourceName,
                                Map<String, Object> eventDetails, boolean success, String errorMessage) {
        try {
            SecurityAuditEvent auditEvent = SecurityAuditEvent.builder(eventType)
                    .description(description)
                    .resourceType(resourceType)
                    .resourceId(resourceId)
                    .resourceName(resourceName)
                    .eventDetails(eventDetails)
                    .success(success)
                    .errorMessage(errorMessage)
                    .build();

            // Populate user context
            populateUserContext(auditEvent);
            
            // Populate request context
            populateRequestContext(auditEvent);

            auditEventRepository.save(auditEvent);
            
            // Log to application logs as well for immediate visibility
            logToApplicationLog(auditEvent);
            
        } catch (Exception e) {
            log.error("Failed to log security audit event: {}", e.getMessage(), e);
        }
    }

    /**
     * Log a role assignment event
     */
    @Async
    public void logRoleAssignment(User user, String roleName, Organization organization, 
                                 User assignedBy, String reason) {
        Map<String, Object> details = Map.of(
            "roleName", roleName,
            "organizationId", organization.getId(),
            "organizationName", organization.getName(),
            "assignedBy", assignedBy.getFullName(),
            "assignedByEmail", assignedBy.getEmail(),
            "reason", reason != null ? reason : "No reason provided"
        );

        SecurityAuditEvent auditEvent = SecurityAuditEvent.builder(SecurityEventType.ROLE_ASSIGNED)
                .description("Role '" + roleName + "' assigned to user '" + user.getFullName() + "'")
                .resourceType("USER")
                .resourceId(user.getId())
                .resourceName(user.getFullName())
                .eventDetails(details)
                .user(assignedBy)
                .userName(assignedBy.getFullName())
                .userEmail(assignedBy.getEmail())
                .organization(organization)
                .organizationName(organization.getName())
                .build();

        populateRequestContext(auditEvent);
        auditEventRepository.save(auditEvent);
        logToApplicationLog(auditEvent);
    }

    /**
     * Log a role revocation event
     */
    @Async
    public void logRoleRevocation(User user, String roleName, Organization organization, 
                                 User revokedBy, String reason) {
        Map<String, Object> details = Map.of(
            "roleName", roleName,
            "organizationId", organization.getId(),
            "organizationName", organization.getName(),
            "revokedBy", revokedBy.getFullName(),
            "revokedByEmail", revokedBy.getEmail(),
            "reason", reason != null ? reason : "No reason provided"
        );

        SecurityAuditEvent auditEvent = SecurityAuditEvent.builder(SecurityEventType.ROLE_REVOKED)
                .description("Role '" + roleName + "' revoked from user '" + user.getFullName() + "'")
                .resourceType("USER")
                .resourceId(user.getId())
                .resourceName(user.getFullName())
                .eventDetails(details)
                .user(revokedBy)
                .userName(revokedBy.getFullName())
                .userEmail(revokedBy.getEmail())
                .organization(organization)
                .organizationName(organization.getName())
                .build();

        populateRequestContext(auditEvent);
        auditEventRepository.save(auditEvent);
        logToApplicationLog(auditEvent);
    }

    /**
     * Log an access denied event
     */
    @Async
    public void logAccessDenied(String resource, String action, String reason) {
        Map<String, Object> details = Map.of(
            "resource", resource,
            "action", action,
            "reason", reason
        );

        logSecurityEvent(SecurityEventType.ACCESS_DENIED, 
                        "Access denied to " + resource + " for action " + action,
                        "RESOURCE", null, resource, details, false, reason);
    }

    /**
     * Get audit events for a specific user
     */
    @Transactional(readOnly = true)
    public Page<SecurityAuditEvent> getAuditEventsForUser(UUID userId, Pageable pageable) {
        return auditEventRepository.findByUserIdOrderByEventTimestampDesc(userId, pageable);
    }

    /**
     * Get audit events for a specific organization
     */
    @Transactional(readOnly = true)
    public Page<SecurityAuditEvent> getAuditEventsForOrganization(UUID organizationId, Pageable pageable) {
        return auditEventRepository.findByOrganizationIdOrderByEventTimestampDesc(organizationId, pageable);
    }

    /**
     * Get audit events by type
     */
    @Transactional(readOnly = true)
    public Page<SecurityAuditEvent> getAuditEventsByType(SecurityEventType eventType, Pageable pageable) {
        return auditEventRepository.findByEventTypeOrderByEventTimestampDesc(eventType, pageable);
    }

    /**
     * Get audit events within date range
     */
    @Transactional(readOnly = true)
    public Page<SecurityAuditEvent> getAuditEventsByDateRange(OffsetDateTime startDate, OffsetDateTime endDate, 
                                                             Pageable pageable) {
        return auditEventRepository.findByEventTimestampBetweenOrderByEventTimestampDesc(
                startDate, endDate, pageable);
    }

    /**
     * Get security concern events (errors and warnings)
     */
    @Transactional(readOnly = true)
    public Page<SecurityAuditEvent> getSecurityConcerns(Pageable pageable) {
        return auditEventRepository.findBySeverityInOrderByEventTimestampDesc(
                java.util.List.of("ERROR", "WARN"), pageable);
    }

    private void populateUserContext(SecurityAuditEvent auditEvent) {
        try {
            Optional<User> currentUser = SecurityUtils.getActorContext() != null ? 
                    Optional.ofNullable(SecurityUtils.getActorContext().getActor()) : Optional.empty();
            
            if (currentUser.isPresent()) {
                User user = currentUser.get();
                auditEvent.setUser(user);
                auditEvent.setUserEmail(user.getEmail());
                auditEvent.setUserName(user.getFullName());
                
                if (user.getOrganization() != null) {
                    auditEvent.setOrganization(user.getOrganization());
                    auditEvent.setOrganizationName(user.getOrganization().getName());
                }
            }
        } catch (Exception e) {
            log.debug("Could not populate user context for audit event: {}", e.getMessage());
        }
    }

    private void populateRequestContext(SecurityAuditEvent auditEvent) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                auditEvent.setIpAddress(getClientIpAddress(request));
                auditEvent.setUserAgent(request.getHeader("User-Agent"));
                auditEvent.setSessionId(request.getSession(false) != null ? request.getSession().getId() : null);
                auditEvent.setRequestId(request.getHeader("X-Request-ID"));
            }
        } catch (Exception e) {
            log.debug("Could not populate request context for audit event: {}", e.getMessage());
        }
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    private void logToApplicationLog(SecurityAuditEvent auditEvent) {
        String logMessage = auditEvent.getSummary();
        
        switch (auditEvent.getSeverity()) {
            case "ERROR" -> log.error("SECURITY_AUDIT: {}", logMessage);
            case "WARN" -> log.warn("SECURITY_AUDIT: {}", logMessage);
            case "INFO" -> log.info("SECURITY_AUDIT: {}", logMessage);
            case "DEBUG" -> log.debug("SECURITY_AUDIT: {}", logMessage);
            default -> log.info("SECURITY_AUDIT: {}", logMessage);
        }
    }
}
