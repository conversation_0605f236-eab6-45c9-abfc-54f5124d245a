package com.smaile.health.security.audit;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration of all security events that should be audited.
 * Each event type represents a significant security-related action in the system.
 */
@Getter
@AllArgsConstructor
public enum SecurityEventType {
    // Authentication Events
    USER_LOGIN("USER_LOGIN", "User successful login", "INFO"),
    USER_LOGIN_FAILED("USER_LOGIN_FAILED", "User failed login attempt", "WARN"),
    USER_LOGOUT("USER_LOGOUT", "User logout", "INFO"),
    USER_SESSION_EXPIRED("USER_SESSION_EXPIRED", "User session expired", "INFO"),
    
    // Authorization Events
    ACCESS_GRANTED("ACCESS_GRANTED", "Access granted to resource", "INFO"),
    ACCESS_DENIED("ACCESS_DENIED", "Access denied to resource", "WARN"),
    PERMI<PERSON><PERSON>_CHECK("PERMISSION_CHECK", "Permission check performed", "DEBUG"),
    
    // Role Management Events
    ROLE_CREATED("ROLE_CREATED", "New role created", "INFO"),
    ROLE_UPDATED("ROLE_UPDATED", "Role updated", "INFO"),
    ROLE_DELETED("ROLE_DELETED", "Role deleted", "WARN"),
    ROLE_ASSIGNED("ROLE_ASSIGNED", "Role assigned to user", "INFO"),
    ROLE_REVOKED("ROLE_REVOKED", "Role revoked from user", "WARN"),
    ROLE_EXPIRED("ROLE_EXPIRED", "Role assignment expired", "INFO"),
    
    // Permission Management Events
    PERMISSION_CREATED("PERMISSION_CREATED", "New permission created", "INFO"),
    PERMISSION_UPDATED("PERMISSION_UPDATED", "Permission updated", "INFO"),
    PERMISSION_DELETED("PERMISSION_DELETED", "Permission deleted", "WARN"),
    PERMISSION_GRANTED("PERMISSION_GRANTED", "Permission granted to role", "INFO"),
    PERMISSION_REMOVED("PERMISSION_REMOVED", "Permission removed from role", "WARN"),
    
    // User Management Events
    USER_CREATED("USER_CREATED", "New user created", "INFO"),
    USER_UPDATED("USER_UPDATED", "User updated", "INFO"),
    USER_DELETED("USER_DELETED", "User deleted", "WARN"),
    USER_ACTIVATED("USER_ACTIVATED", "User activated", "INFO"),
    USER_DEACTIVATED("USER_DEACTIVATED", "User deactivated", "WARN"),
    USER_LOCKED("USER_LOCKED", "User account locked", "WARN"),
    USER_UNLOCKED("USER_UNLOCKED", "User account unlocked", "INFO"),
    
    // Organization Events
    ORGANIZATION_CREATED("ORGANIZATION_CREATED", "New organization created", "INFO"),
    ORGANIZATION_UPDATED("ORGANIZATION_UPDATED", "Organization updated", "INFO"),
    ORGANIZATION_DELETED("ORGANIZATION_DELETED", "Organization deleted", "WARN"),
    ORGANIZATION_HIERARCHY_CHANGED("ORGANIZATION_HIERARCHY_CHANGED", "Organization hierarchy changed", "WARN"),
    
    // Security Configuration Events
    SECURITY_POLICY_CHANGED("SECURITY_POLICY_CHANGED", "Security policy changed", "WARN"),
    SECURITY_CONFIGURATION_UPDATED("SECURITY_CONFIGURATION_UPDATED", "Security configuration updated", "WARN"),
    
    // Suspicious Activities
    MULTIPLE_FAILED_LOGINS("MULTIPLE_FAILED_LOGINS", "Multiple failed login attempts", "ERROR"),
    PRIVILEGE_ESCALATION_ATTEMPT("PRIVILEGE_ESCALATION_ATTEMPT", "Privilege escalation attempt", "ERROR"),
    UNAUTHORIZED_ACCESS_ATTEMPT("UNAUTHORIZED_ACCESS_ATTEMPT", "Unauthorized access attempt", "ERROR"),
    SUSPICIOUS_ACTIVITY("SUSPICIOUS_ACTIVITY", "Suspicious activity detected", "ERROR"),
    
    // System Events
    SYSTEM_STARTUP("SYSTEM_STARTUP", "System startup", "INFO"),
    SYSTEM_SHUTDOWN("SYSTEM_SHUTDOWN", "System shutdown", "INFO"),
    SECURITY_SCAN_COMPLETED("SECURITY_SCAN_COMPLETED", "Security scan completed", "INFO"),
    
    // Data Events
    SENSITIVE_DATA_ACCESS("SENSITIVE_DATA_ACCESS", "Sensitive data accessed", "INFO"),
    DATA_EXPORT("DATA_EXPORT", "Data exported", "WARN"),
    DATA_IMPORT("DATA_IMPORT", "Data imported", "INFO"),
    
    // Administrative Events
    ADMIN_ACTION("ADMIN_ACTION", "Administrative action performed", "INFO"),
    BULK_OPERATION("BULK_OPERATION", "Bulk operation performed", "INFO"),
    SYSTEM_MAINTENANCE("SYSTEM_MAINTENANCE", "System maintenance performed", "INFO");

    private final String code;
    private final String description;
    private final String severity;

    /**
     * Get event type by code
     */
    public static SecurityEventType fromCode(String code) {
        for (SecurityEventType eventType : values()) {
            if (eventType.code.equals(code)) {
                return eventType;
            }
        }
        throw new IllegalArgumentException("Unknown security event type code: " + code);
    }

    /**
     * Check if this event type should be logged at the specified level
     */
    public boolean shouldLog(String logLevel) {
        return switch (logLevel.toUpperCase()) {
            case "ERROR" -> "ERROR".equals(severity);
            case "WARN" -> "ERROR".equals(severity) || "WARN".equals(severity);
            case "INFO" -> !"DEBUG".equals(severity);
            case "DEBUG" -> true;
            default -> false;
        };
    }
}
