package com.smaile.health.security.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration of all security resources in the system.
 * Each resource represents a domain entity or functional area that can be secured.
 */
@Getter
@AllArgsConstructor
public enum Resource {
    // Organization Management
    ORGANIZATION("organization", "Organization management"),
    USER("user", "User management"),
    ROLE("role", "Role management"),
    PERMISSION("permission", "Permission management"),
    
    // Healthcare Entities
    INSURANCE_COMPANY("insurance_company", "Insurance Company management"),
    TPA("tpa", "Third Party Administrator management"),
    MEDICAL_PROVIDER("medical_provider", "Medical Provider management"),
    PROFESSIONAL("professional", "Healthcare Professional management"),
    
    // Clinical Data
    PATIENT("patient", "Patient data management"),
    MEDICAL_RECORD("medical_record", "Medical record management"),
    APPOINTMENT("appointment", "Appointment management"),
    CLAIM("claim", "Insurance claim management"),
    
    // System Administration
    SYSTEM("system", "System administration"),
    AUDIT("audit", "Audit log management"),
    REPORT("report", "Report generation and access"),
    CONFIGURATION("configuration", "System configuration");

    private final String code;
    private final String description;

    /**
     * Get resource by code
     */
    public static Resource fromCode(String code) {
        for (Resource resource : values()) {
            if (resource.code.equals(code)) {
                return resource;
            }
        }
        throw new IllegalArgumentException("Unknown security resource code: " + code);
    }
}
