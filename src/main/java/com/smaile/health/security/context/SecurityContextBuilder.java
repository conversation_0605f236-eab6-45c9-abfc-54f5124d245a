package com.smaile.health.security.context;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.google.common.collect.Multimaps;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.Permission;
import com.smaile.health.domain.Role;
import com.smaile.health.domain.User;
import com.smaile.health.domain.projection.UserOrganizationRoles;
import com.smaile.health.repository.RoleRepository;
import com.smaile.health.repository.UserRoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service responsible for building security context with permissions for each organization
 * after successful authentication.
 */
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
@Slf4j
public class SecurityContextBuilder {

    private final UserRoleRepository userRoleRepository;
    private final RoleRepository roleRepository;

    /**
     * Build organization-specific permission contexts for a user
     */
    public Map<UUID, OrganizationPermissionContext> buildOrganizationPermissionContexts(User user) {
        log.debug("Building permission contexts for user: {}", user.getEmail());

        Map<UUID, OrganizationPermissionContext> contexts = new HashMap<>();
        Multimap<String, Permission> permissionsByRoles = getPermissionsByRoles();
        List<UserOrganizationRoles> activeUserRoles = getActiveUserRoles(user);
        ArrayListMultimap<UUID, UserOrganizationRoles> userRolesByOrganization = activeUserRoles.stream()
                .collect(Multimaps.toMultimap(
                        UserOrganizationRoles::getOrganizationId,
                        userOrganizationRoles -> userOrganizationRoles,
                        ArrayListMultimap::create
                ));

        userRolesByOrganization.asMap().forEach((orgId, uor) -> {
            List<UserOrganizationRoles> userOrganizationRoles = uor.stream().toList();
            UUID organizationId = userOrganizationRoles.get(0).getOrganizationId();
            String organizationName = userOrganizationRoles.get(0).getOrganizationName();
            OrganizationPermissionContext context = buildDirectPermissionContext(organizationId, organizationName,
                    userOrganizationRoles, permissionsByRoles);
            contexts.put(orgId, context);
        });

        log.debug("Built {} permission contexts for user: {}", contexts.size(), user.getEmail());
        return contexts;
    }

    private ArrayListMultimap<String, Permission> getPermissionsByRoles() {
        List<Role> roles = roleRepository.findAll();
        return roles.stream()
                .collect(ArrayListMultimap::create,
                        (map, role) -> role.getPermissions()
                                .forEach(permission -> map.put(role.getCode(), permission)),
                        Multimap::putAll
                );
    }

    /**
     * Build permission context for direct role assignments in an organization
     */
    private OrganizationPermissionContext buildDirectPermissionContext(UUID organizationId, String organizationName,
                                                                       List<UserOrganizationRoles> userOrganizationRoles,
                                                                       Multimap<String, Permission> userRoles) {
        Set<String> permissions = new HashSet<>();
        Set<String> roles = new HashSet<>();

        for (UserOrganizationRoles userRole : userOrganizationRoles) {
            String roleCode = userRole.getRoleCode();
            roles.add(roleCode);

            if (userRoles.containsKey(roleCode)) {
                Set<String> rolePermissions = userRoles.get(roleCode).stream()
                        .map(this::formatPermission)
                        .collect(Collectors.toSet());
                permissions.addAll(rolePermissions);
            }
        }

        return OrganizationPermissionContext.builder()
                .organizationId(organizationId)
                .organizationName(organizationName)
                .permissions(permissions)
                .roles(roles)
                .build();
    }

    //    /**
    //     * Add inherited permissions from parent organizations
    //     */
    //    private void addInheritedPermissions(User user, Map<UUID, OrganizationPermissionContext> contexts) {
    //        Map<UUID, OrganizationPermissionContext> inheritedContexts = new HashMap<>();
    //
    //        for (OrganizationPermissionContext context : contexts.values()) {
    //            organizationRepository.findById(context.getOrganizationId()).ifPresent(
    //                    organization -> addInheritedPermissionsFromParents(user, organization, inheritedContexts,
    //                            contexts));
    //        }
    //
    //        contexts.putAll(inheritedContexts);
    //    }
    //
    //    /**
    //     * Recursively add inherited permissions from parent organizations
    //     */
    //    private void addInheritedPermissionsFromParents(User user, Organization organization,
    //                                                    Map<UUID, OrganizationPermissionContext> inheritedContexts,
    //                                                    Map<UUID, OrganizationPermissionContext> directContexts) {
    //
    //        Organization parentOrg = organization.getParent();
    //        if (parentOrg == null) {
    //            return;
    //        }
    //
    //        List<UserOrganizationRoles> parentRoles = getActiveUserRolesInOrganization(user, parentOrg);
    //
    //        if (!parentRoles.isEmpty()) {
    //            // Get inheritable permissions from parent organization
    //            Set<String> inheritablePermissions = new HashSet<>();
    //            Set<String> inheritableRoles = new HashSet<>();
    //
    //            for (UserOrganizationRoles userRole : parentRoles) {
    //                Role role = userRole.get();
    //
    //                // Only inherit if role is marked as inheritable (assuming this field exists)
    //                if (isRoleInheritable(role)) {
    //                    inheritableRoles.add(role.getCode());
    //
    //                    if (role.getPermissions() != null) {
    //                        Set<String> rolePermissions = role.getPermissions().stream()
    //                                .filter(this::isPermissionInheritable)
    //                                .map(this::formatPermission)
    //                                .collect(Collectors.toSet());
    //                        inheritablePermissions.addAll(rolePermissions);
    //                    }
    //                }
    //            }
    //
    //            // Create inherited context for the current organization
    //            if (!inheritablePermissions.isEmpty() && !directContexts.containsKey(organization.getId())) {
    //                OrganizationPermissionContext inheritedContext = OrganizationPermissionContext.builder()
    //                        .organizationId(organization.getId())
    //                        .organizationName(organization.getName())
    //                        .permissions(inheritablePermissions)
    //                        .roles(inheritableRoles)
    //                        .build();
    //
    //                inheritedContexts.put(organization.getId(), inheritedContext);
    //            }
    //        }
    //
    //        addInheritedPermissionsFromParents(user, parentOrg, inheritedContexts, directContexts);
    //    }

    /**
     * Get active user roles for a user
     */
    private List<UserOrganizationRoles> getActiveUserRoles(User user) {
        return userRoleRepository.findByUserId(user.getId());
    }

    /**
     * Get active user roles in a specific organization
     */
    private List<UserOrganizationRoles> getActiveUserRolesInOrganization(User user, Organization organization) {
        return getActiveUserRoles(user).stream()
                .filter(ur -> ur.getOrganizationId().equals(organization.getId()))
                .toList();
    }

    /**
     * Check if a permission is inheritable to child organizations
     */
    private boolean isPermissionInheritable(Permission permission) {
        // Check if permission scope allows inheritance
        // This would depend on your permission structure
        return permission.getResource() != null &&
                (permission.getResource().contains("global") ||
                        permission.getResource().contains("organization"));
    }

    /**
     * Format permission as string
     */
    private String formatPermission(Permission permission) {
        StringBuilder sb = new StringBuilder();

        if (permission.getResource() != null) {
            sb.append(permission.getResource());
        }

        if (permission.getSubResource() != null) {
            sb.append(":").append(permission.getSubResource());
        }

        if (permission.getAction() != null) {
            sb.append(":").append(permission.getAction());
        }

        return sb.toString();
    }

    /**
     * Build simplified permission map for SmaileAuthentication
     */
    public Map<UUID, Set<String>> buildPermissionMap(Map<UUID, OrganizationPermissionContext> contexts) {
        return contexts.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().getPermissions()
                ));
    }

    /**
     * Build role set for SmaileAuthentication
     */
    public Set<String> buildRoleSet(Map<UUID, OrganizationPermissionContext> contexts) {
        return contexts.values().stream()
                .flatMap(context -> context.getRoles().stream())
                .collect(Collectors.toSet());
    }

}
