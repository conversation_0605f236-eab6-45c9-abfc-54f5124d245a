package com.smaile.health.security.repository;

import com.smaile.health.security.model.SecurityAction;
import com.smaile.health.security.model.SecurityResource;
import com.smaile.health.security.model.SecurityScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for EnhancedPermission entities.
 * Provides methods to query permissions with various filters and combinations.
 */
@Repository
public interface EnhancedPermissionRepository extends JpaRepository<EnhancedPermission, UUID>, 
                                                     JpaSpecificationExecutor<EnhancedPermission> {

    /**
     * Find permission by resource, action, scope, and condition
     */
    Optional<EnhancedPermission> findByResourceAndActionAndScopeAndCondition(
            SecurityResource resource, SecurityAction action, SecurityScope scope, String condition);

    /**
     * Find permission by resource, action, and scope (no condition)
     */
    Optional<EnhancedPermission> findByResourceAndActionAndScopeAndConditionIsNull(
            SecurityResource resource, SecurityAction action, SecurityScope scope);

    /**
     * Find permissions by resource
     */
    List<EnhancedPermission> findByResourceAndStatus(SecurityResource resource, String status);

    /**
     * Find permissions by action
     */
    List<EnhancedPermission> findByActionAndStatus(SecurityAction action, String status);

    /**
     * Find permissions by scope
     */
    List<EnhancedPermission> findByScopeAndStatus(SecurityScope scope, String status);

    /**
     * Find permissions by resource and action
     */
    List<EnhancedPermission> findByResourceAndActionAndStatus(SecurityResource resource, SecurityAction action, String status);

    /**
     * Find system permissions
     */
    List<EnhancedPermission> findBySystemPermissionTrueAndStatus(String status);

    /**
     * Find permissions with conditions
     */
    List<EnhancedPermission> findByConditionIsNotNullAndStatus(String status);

    /**
     * Find permissions without conditions
     */
    List<EnhancedPermission> findByConditionIsNullAndStatus(String status);

    /**
     * Find permissions by name pattern
     */
    @Query("SELECT p FROM EnhancedPermission p WHERE LOWER(p.name) LIKE LOWER(CONCAT('%', :namePattern, '%')) " +
           "AND p.status = :status")
    Page<EnhancedPermission> findByNameContainingIgnoreCase(@Param("namePattern") String namePattern,
                                                           @Param("status") String status,
                                                           Pageable pageable);

    /**
     * Find permissions that imply a specific permission
     */
    @Query("SELECT p FROM EnhancedPermission p WHERE p.resource = :resource " +
           "AND (p.action = :action OR p.action = 'MANAGE') " +
           "AND p.status = :status")
    List<EnhancedPermission> findPermissionsThatImply(@Param("resource") SecurityResource resource,
                                                     @Param("action") SecurityAction action,
                                                     @Param("status") String status);

    /**
     * Find permissions by multiple resources
     */
    @Query("SELECT p FROM EnhancedPermission p WHERE p.resource IN :resources AND p.status = :status")
    List<EnhancedPermission> findByResourceInAndStatus(@Param("resources") List<SecurityResource> resources,
                                                       @Param("status") String status);

    /**
     * Find permissions by multiple actions
     */
    @Query("SELECT p FROM EnhancedPermission p WHERE p.action IN :actions AND p.status = :status")
    List<EnhancedPermission> findByActionInAndStatus(@Param("actions") List<SecurityAction> actions,
                                                     @Param("status") String status);

    /**
     * Find permissions by multiple scopes
     */
    @Query("SELECT p FROM EnhancedPermission p WHERE p.scope IN :scopes AND p.status = :status")
    List<EnhancedPermission> findByScopeInAndStatus(@Param("scopes") List<SecurityScope> scopes,
                                                    @Param("status") String status);

    /**
     * Count permissions by resource
     */
    long countByResourceAndStatus(SecurityResource resource, String status);

    /**
     * Count permissions by action
     */
    long countByActionAndStatus(SecurityAction action, String status);

    /**
     * Count permissions by scope
     */
    long countByScopeAndStatus(SecurityScope scope, String status);

    /**
     * Find permissions assigned to roles
     */
    @Query("SELECT DISTINCT p FROM EnhancedPermission p JOIN p.roles r WHERE r.status = :roleStatus " +
           "AND p.status = :permissionStatus")
    List<EnhancedPermission> findPermissionsAssignedToActiveRoles(@Param("roleStatus") String roleStatus,
                                                                 @Param("permissionStatus") String permissionStatus);

    /**
     * Find unassigned permissions (not assigned to any role)
     */
    @Query("SELECT p FROM EnhancedPermission p WHERE p.roles IS EMPTY AND p.status = :status")
    List<EnhancedPermission> findUnassignedPermissions(@Param("status") String status);

    /**
     * Find permissions assigned to a specific role
     */
    @Query("SELECT p FROM EnhancedPermission p JOIN p.roles r WHERE r.id = :roleId AND p.status = :status")
    List<EnhancedPermission> findPermissionsByRole(@Param("roleId") UUID roleId, @Param("status") String status);

    /**
     * Find permissions that can be inherited (global or organization tree scope)
     */
    @Query("SELECT p FROM EnhancedPermission p WHERE p.scope IN ('GLOBAL', 'ORGANIZATION_TREE', 'CHILD_ORGANIZATIONS') " +
           "AND p.status = :status")
    List<EnhancedPermission> findInheritablePermissions(@Param("status") String status);

    /**
     * Find permissions for specific resource and scope combination
     */
    @Query("SELECT p FROM EnhancedPermission p WHERE p.resource = :resource AND p.scope = :scope " +
           "AND p.status = :status ORDER BY p.action")
    List<EnhancedPermission> findByResourceAndScope(@Param("resource") SecurityResource resource,
                                                   @Param("scope") SecurityScope scope,
                                                   @Param("status") String status);

    /**
     * Get permission statistics by resource
     */
    @Query("SELECT p.resource, COUNT(p), COUNT(DISTINCT p.action) as action_count " +
           "FROM EnhancedPermission p WHERE p.status = :status " +
           "GROUP BY p.resource ORDER BY COUNT(p) DESC")
    List<Object[]> getPermissionStatisticsByResource(@Param("status") String status);

    /**
     * Get permission statistics by action
     */
    @Query("SELECT p.action, COUNT(p), COUNT(DISTINCT p.resource) as resource_count " +
           "FROM EnhancedPermission p WHERE p.status = :status " +
           "GROUP BY p.action ORDER BY COUNT(p) DESC")
    List<Object[]> getPermissionStatisticsByAction(@Param("status") String status);

    /**
     * Get permission statistics by scope
     */
    @Query("SELECT p.scope, COUNT(p), COUNT(DISTINCT p.resource) as resource_count " +
           "FROM EnhancedPermission p WHERE p.status = :status " +
           "GROUP BY p.scope ORDER BY COUNT(p) DESC")
    List<Object[]> getPermissionStatisticsByScope(@Param("status") String status);

    /**
     * Find permissions created recently
     */
    @Query("SELECT p FROM EnhancedPermission p WHERE p.dateCreated >= :since AND p.status = :status " +
           "ORDER BY p.dateCreated DESC")
    List<EnhancedPermission> findRecentlyCreatedPermissions(@Param("since") java.time.OffsetDateTime since,
                                                           @Param("status") String status);

    /**
     * Find permissions modified recently
     */
    @Query("SELECT p FROM EnhancedPermission p WHERE p.lastUpdated >= :since AND p.status = :status " +
           "ORDER BY p.lastUpdated DESC")
    List<EnhancedPermission> findRecentlyModifiedPermissions(@Param("since") java.time.OffsetDateTime since,
                                                            @Param("status") String status);

    /**
     * Find permissions with specific condition pattern
     */
    @Query("SELECT p FROM EnhancedPermission p WHERE p.condition LIKE :conditionPattern AND p.status = :status")
    List<EnhancedPermission> findByConditionPattern(@Param("conditionPattern") String conditionPattern,
                                                   @Param("status") String status);

    /**
     * Find all unique resource-action combinations
     */
    @Query("SELECT DISTINCT p.resource, p.action FROM EnhancedPermission p WHERE p.status = :status " +
           "ORDER BY p.resource, p.action")
    List<Object[]> findUniqueResourceActionCombinations(@Param("status") String status);

    /**
     * Find permissions that are most commonly assigned
     */
    @Query("SELECT p, COUNT(r) as role_count FROM EnhancedPermission p LEFT JOIN p.roles r " +
           "WHERE p.status = :status GROUP BY p ORDER BY COUNT(r) DESC")
    List<Object[]> findMostAssignedPermissions(@Param("status") String status, Pageable pageable);

    /**
     * Check if permission string exists
     */
    @Query("SELECT COUNT(p) > 0 FROM EnhancedPermission p WHERE p.resource = :resource " +
           "AND p.action = :action AND p.scope = :scope " +
           "AND (:condition IS NULL AND p.condition IS NULL OR p.condition = :condition) " +
           "AND p.status = :status")
    boolean existsByPermissionString(@Param("resource") SecurityResource resource,
                                   @Param("action") SecurityAction action,
                                   @Param("scope") SecurityScope scope,
                                   @Param("condition") String condition,
                                   @Param("status") String status);
}
