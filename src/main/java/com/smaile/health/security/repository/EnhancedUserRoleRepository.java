package com.smaile.health.security.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for EnhancedUserRole entities.
 * Provides methods to query user role assignments with temporal and organizational filters.
 */
@Repository
public interface EnhancedUserRoleRepository extends JpaRepository<EnhancedUserRole, UUID>, 
                                                   JpaSpecificationExecutor<EnhancedUserRole> {

    /**
     * Find active roles for a user
     */
    @Query("SELECT ur FROM EnhancedUserRole ur WHERE ur.user.id = :userId " +
           "AND ur.status = 'ACTIVE' AND ur.revokedAt IS NULL " +
           "AND (ur.validFrom IS NULL OR ur.validFrom <= CURRENT_TIMESTAMP) " +
           "AND (ur.validTo IS NULL OR ur.validTo > CURRENT_TIMESTAMP)")
    List<EnhancedUserRole> findActiveRolesByUser(@Param("userId") UUID userId);

    /**
     * Find active roles for a user in a specific organization
     */
    @Query("SELECT ur FROM EnhancedUserRole ur WHERE ur.user.id = :userId " +
           "AND ur.organization.id = :organizationId " +
           "AND ur.status = 'ACTIVE' AND ur.revokedAt IS NULL " +
           "AND (ur.validFrom IS NULL OR ur.validFrom <= CURRENT_TIMESTAMP) " +
           "AND (ur.validTo IS NULL OR ur.validTo > CURRENT_TIMESTAMP)")
    List<EnhancedUserRole> findActiveRolesByUserAndOrganization(@Param("userId") UUID userId,
                                                               @Param("organizationId") UUID organizationId);

    /**
     * Find all roles for a user (including inactive)
     */
    List<EnhancedUserRole> findByUserIdOrderByValidFromDesc(UUID userId);

    /**
     * Find all roles in an organization
     */
    List<EnhancedUserRole> findByOrganizationIdOrderByValidFromDesc(UUID organizationId);

    /**
     * Find specific user role assignment
     */
    Optional<EnhancedUserRole> findByUserIdAndRoleIdAndOrganizationId(UUID userId, UUID roleId, UUID organizationId);

    /**
     * Find roles assigned by a specific user
     */
    List<EnhancedUserRole> findByAssignedByUserIdOrderByValidFromDesc(UUID assignedByUserId);

    /**
     * Find roles revoked by a specific user
     */
    List<EnhancedUserRole> findByRevokedByUserIdOrderByRevokedAtDesc(UUID revokedByUserId);

    /**
     * Find roles that expire within a time period
     */
    @Query("SELECT ur FROM EnhancedUserRole ur WHERE ur.validTo IS NOT NULL " +
           "AND ur.validTo BETWEEN :startDate AND :endDate " +
           "AND ur.status = 'ACTIVE' AND ur.revokedAt IS NULL")
    List<EnhancedUserRole> findRolesExpiringBetween(@Param("startDate") OffsetDateTime startDate,
                                                    @Param("endDate") OffsetDateTime endDate);

    /**
     * Find expired roles that are still active
     */
    @Query("SELECT ur FROM EnhancedUserRole ur WHERE ur.validTo IS NOT NULL " +
           "AND ur.validTo < CURRENT_TIMESTAMP " +
           "AND ur.status = 'ACTIVE' AND ur.revokedAt IS NULL")
    List<EnhancedUserRole> findExpiredActiveRoles();

    /**
     * Find roles valid at a specific time
     */
    @Query("SELECT ur FROM EnhancedUserRole ur WHERE ur.user.id = :userId " +
           "AND (ur.validFrom IS NULL OR ur.validFrom <= :dateTime) " +
           "AND (ur.validTo IS NULL OR ur.validTo > :dateTime) " +
           "AND (ur.revokedAt IS NULL OR ur.revokedAt > :dateTime) " +
           "AND ur.status = 'ACTIVE'")
    List<EnhancedUserRole> findRolesValidAt(@Param("userId") UUID userId,
                                           @Param("dateTime") OffsetDateTime dateTime);

    /**
     * Find users with a specific role in an organization
     */
    @Query("SELECT ur FROM EnhancedUserRole ur WHERE ur.role.id = :roleId " +
           "AND ur.organization.id = :organizationId " +
           "AND ur.status = 'ACTIVE' AND ur.revokedAt IS NULL " +
           "AND (ur.validFrom IS NULL OR ur.validFrom <= CURRENT_TIMESTAMP) " +
           "AND (ur.validTo IS NULL OR ur.validTo > CURRENT_TIMESTAMP)")
    List<EnhancedUserRole> findUsersWithRoleInOrganization(@Param("roleId") UUID roleId,
                                                           @Param("organizationId") UUID organizationId);

    /**
     * Find users with a specific role code in an organization
     */
    @Query("SELECT ur FROM EnhancedUserRole ur WHERE ur.role.code = :roleCode " +
           "AND ur.organization.id = :organizationId " +
           "AND ur.status = 'ACTIVE' AND ur.revokedAt IS NULL " +
           "AND (ur.validFrom IS NULL OR ur.validFrom <= CURRENT_TIMESTAMP) " +
           "AND (ur.validTo IS NULL OR ur.validTo > CURRENT_TIMESTAMP)")
    List<EnhancedUserRole> findUsersWithRoleCodeInOrganization(@Param("roleCode") String roleCode,
                                                              @Param("organizationId") UUID organizationId);

    /**
     * Count active roles for a user
     */
    @Query("SELECT COUNT(ur) FROM EnhancedUserRole ur WHERE ur.user.id = :userId " +
           "AND ur.status = 'ACTIVE' AND ur.revokedAt IS NULL " +
           "AND (ur.validFrom IS NULL OR ur.validFrom <= CURRENT_TIMESTAMP) " +
           "AND (ur.validTo IS NULL OR ur.validTo > CURRENT_TIMESTAMP)")
    long countActiveRolesByUser(@Param("userId") UUID userId);

    /**
     * Count users with active roles in an organization
     */
    @Query("SELECT COUNT(DISTINCT ur.user.id) FROM EnhancedUserRole ur " +
           "WHERE ur.organization.id = :organizationId " +
           "AND ur.status = 'ACTIVE' AND ur.revokedAt IS NULL " +
           "AND (ur.validFrom IS NULL OR ur.validFrom <= CURRENT_TIMESTAMP) " +
           "AND (ur.validTo IS NULL OR ur.validTo > CURRENT_TIMESTAMP)")
    long countActiveUsersInOrganization(@Param("organizationId") UUID organizationId);

    /**
     * Find role assignments created within date range
     */
    @Query("SELECT ur FROM EnhancedUserRole ur WHERE ur.dateCreated BETWEEN :startDate AND :endDate " +
           "ORDER BY ur.dateCreated DESC")
    Page<EnhancedUserRole> findRoleAssignmentsCreatedBetween(@Param("startDate") OffsetDateTime startDate,
                                                            @Param("endDate") OffsetDateTime endDate,
                                                            Pageable pageable);

    /**
     * Find role assignments with specific assignment reason
     */
    @Query("SELECT ur FROM EnhancedUserRole ur WHERE LOWER(ur.assignmentReason) LIKE LOWER(CONCAT('%', :reason, '%')) " +
           "ORDER BY ur.dateCreated DESC")
    List<EnhancedUserRole> findByAssignmentReasonContaining(@Param("reason") String reason);

    /**
     * Find temporary role assignments (with expiration date)
     */
    @Query("SELECT ur FROM EnhancedUserRole ur WHERE ur.validTo IS NOT NULL " +
           "AND ur.status = 'ACTIVE' AND ur.revokedAt IS NULL " +
           "ORDER BY ur.validTo ASC")
    List<EnhancedUserRole> findTemporaryRoleAssignments();

    /**
     * Find permanent role assignments (without expiration date)
     */
    @Query("SELECT ur FROM EnhancedUserRole ur WHERE ur.validTo IS NULL " +
           "AND ur.status = 'ACTIVE' AND ur.revokedAt IS NULL " +
           "ORDER BY ur.validFrom DESC")
    List<EnhancedUserRole> findPermanentRoleAssignments();

    /**
     * Find role assignments that need review (expiring soon)
     */
    @Query("SELECT ur FROM EnhancedUserRole ur WHERE ur.validTo IS NOT NULL " +
           "AND ur.validTo BETWEEN CURRENT_TIMESTAMP AND :reviewDate " +
           "AND ur.status = 'ACTIVE' AND ur.revokedAt IS NULL " +
           "ORDER BY ur.validTo ASC")
    List<EnhancedUserRole> findRoleAssignmentsNeedingReview(@Param("reviewDate") OffsetDateTime reviewDate);

    /**
     * Get role assignment statistics for an organization
     */
    @Query("SELECT ur.role.name, COUNT(ur), " +
           "SUM(CASE WHEN ur.validTo IS NULL THEN 1 ELSE 0 END) as permanent_count, " +
           "SUM(CASE WHEN ur.validTo IS NOT NULL THEN 1 ELSE 0 END) as temporary_count " +
           "FROM EnhancedUserRole ur WHERE ur.organization.id = :organizationId " +
           "AND ur.status = 'ACTIVE' AND ur.revokedAt IS NULL " +
           "GROUP BY ur.role.name ORDER BY COUNT(ur) DESC")
    List<Object[]> getRoleAssignmentStatistics(@Param("organizationId") UUID organizationId);

    /**
     * Find role assignments by status
     */
    List<EnhancedUserRole> findByStatusOrderByValidFromDesc(String status);

    /**
     * Find recently revoked role assignments
     */
    @Query("SELECT ur FROM EnhancedUserRole ur WHERE ur.revokedAt IS NOT NULL " +
           "AND ur.revokedAt >= :since ORDER BY ur.revokedAt DESC")
    List<EnhancedUserRole> findRecentlyRevokedRoles(@Param("since") OffsetDateTime since);

    /**
     * Check if user has specific role in organization at given time
     */
    @Query("SELECT COUNT(ur) > 0 FROM EnhancedUserRole ur WHERE ur.user.id = :userId " +
           "AND ur.role.code = :roleCode AND ur.organization.id = :organizationId " +
           "AND (ur.validFrom IS NULL OR ur.validFrom <= :dateTime) " +
           "AND (ur.validTo IS NULL OR ur.validTo > :dateTime) " +
           "AND (ur.revokedAt IS NULL OR ur.revokedAt > :dateTime) " +
           "AND ur.status = 'ACTIVE'")
    boolean hasRoleAtTime(@Param("userId") UUID userId,
                         @Param("roleCode") String roleCode,
                         @Param("organizationId") UUID organizationId,
                         @Param("dateTime") OffsetDateTime dateTime);
}
