package com.smaile.health.security.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation for method-level role checking.
 * <p>
 * This annotation can be used on controller methods or service methods
 * to declaratively specify required roles for access.
 * <p>
 * Usage Examples:
 * <p>
 * Single role:
 * @RequireRole(value = "ADMIN")
 * <p>
 * Multiple roles (ANY):
 * @RequireRole(value = {"ADMIN", "MANAGER"}, logic = Logic.ANY)
 * <p>
 * Multiple roles (ALL):
 * @RequireRole(value = {"USER", "VERIFIED"}, logic = Logic.ALL)
 * <p>
 * Organization-specific:
 * @RequireRole(value = "ADMIN", organizationParam = "organizationId")
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/26
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequireRole {

    /**
     * The role code(s) required for access.
     * Examples: "ADMIN", "MANAGER", "USER", "IC_ADMIN", "TPA_STAFF"
     */
    String[] value();

    /**
     * Logic for combining multiple roles.
     * ANY: User needs at least one of the specified roles
     * ALL: User needs all the specified roles
     */
    Logic logic() default Logic.ANY;

    /**
     * Name of the method parameter that contains the organization ID.
     * If specified, the role check will be organization-specific.
     * If not specified, the role check will be global.
     */
    String organizationParam() default "";

    /**
     * Error message to return when role is not present.
     * If not specified, a default message will be used.
     */
    String message() default "";

    /**
     * Logic enumeration for combining multiple roles.
     */
    enum Logic {
        ANY, ALL
    }
}
