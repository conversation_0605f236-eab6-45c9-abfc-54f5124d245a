package com.smaile.health.security.aspect;

import com.smaile.health.security.annotation.RequireAccessLevel;
import com.smaile.health.security.annotation.RequirePermission;
import com.smaile.health.security.annotation.RequireRole;
import com.smaile.health.security.rbac.RoleBasedAccessControlService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Arrays;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Aspect for handling security annotations.
 * <p>
 * This aspect intercepts method calls annotated with security annotations
 * and performs the appropriate authorization checks using the RBAC service.
 * <p>
 * Supported Annotations:
 * - @RequirePermission: Checks for specific permissions
 * - @RequireRole: Checks for specific roles
 * - @RequireAccessLevel: Checks for minimum access level
 * <p>
 * Features:
 * - Automatic parameter extraction for organization context
 * - Support for multiple permissions/roles with AND/OR logic
 * - Comprehensive error messages
 * - Performance logging
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/25
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class SecurityAnnotationAspect {

    private final RoleBasedAccessControlService rbacService;

    /**
     * Handles @RequirePermission annotation.
     * 
     * @param joinPoint The method join point
     * @param requirePermission The annotation instance
     * @throws AccessDeniedException if permission check fails
     */
    @Before("@annotation(requirePermission)")
    public void checkPermission(JoinPoint joinPoint, RequirePermission requirePermission) 
            throws AccessDeniedException {
        
        long startTime = System.currentTimeMillis();
        
        try {
            String resource = requirePermission.resource();
            String[] actions = requirePermission.action();
            RequirePermission.Logic logic = requirePermission.logic();
            String organizationParam = requirePermission.organizationParam();
            
            UUID organizationId = extractOrganizationId(joinPoint, organizationParam);
            
            boolean hasAccess = checkPermissions(resource, actions, logic, organizationId);
            
            if (!hasAccess) {
                String message = StringUtils.hasText(requirePermission.message()) 
                    ? requirePermission.message()
                    : String.format("Access denied. Required permission: %s:%s", resource, String.join(",", actions));
                
                log.warn("Permission denied for method {}: {}", joinPoint.getSignature().toShortString(), message);
                throw new AccessDeniedException(message);
            }
            
            log.debug("Permission check passed for method {} in {}ms", 
                     joinPoint.getSignature().toShortString(), System.currentTimeMillis() - startTime);
            
        } catch (AccessDeniedException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error during permission check for method {}", joinPoint.getSignature().toShortString(), e);
            throw new AccessDeniedException("Permission check failed due to system error");
        }
    }

    /**
     * Handles @RequireRole annotation.
     * 
     * @param joinPoint The method join point
     * @param requireRole The annotation instance
     * @throws AccessDeniedException if role check fails
     */
    @Before("@annotation(requireRole)")
    public void checkRole(JoinPoint joinPoint, RequireRole requireRole) throws AccessDeniedException {
        
        long startTime = System.currentTimeMillis();
        
        try {
            String[] roles = requireRole.value();
            RequireRole.Logic logic = requireRole.logic();
            String organizationParam = requireRole.organizationParam();
            
            // Extract organization ID from method parameters if specified
            UUID organizationId = extractOrganizationId(joinPoint, organizationParam);
            
            // Check roles based on logic
            boolean hasAccess = checkRoles(roles, logic, organizationId);
            
            if (!hasAccess) {
                String message = StringUtils.hasText(requireRole.message()) 
                    ? requireRole.message()
                    : String.format("Access denied. Required role: %s", String.join(",", roles));
                
                log.warn("Role check failed for method {}: {}", joinPoint.getSignature().toShortString(), message);
                throw new AccessDeniedException(message);
            }
            
            log.debug("Role check passed for method {} in {}ms", 
                     joinPoint.getSignature().toShortString(), System.currentTimeMillis() - startTime);
            
        } catch (AccessDeniedException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error during role check for method {}", joinPoint.getSignature().toShortString(), e);
            throw new AccessDeniedException("Role check failed due to system error");
        }
    }

    /**
     * Handles @RequireAccessLevel annotation.
     * 
     * @param joinPoint The method join point
     * @param requireAccessLevel The annotation instance
     * @throws AccessDeniedException if access level check fails
     */
    @Before("@annotation(requireAccessLevel)")
    public void checkAccessLevel(JoinPoint joinPoint, RequireAccessLevel requireAccessLevel) 
            throws AccessDeniedException {
        
        long startTime = System.currentTimeMillis();
        
        try {
            RoleBasedAccessControlService.AccessLevel requiredLevel = requireAccessLevel.value();
            String organizationParam = requireAccessLevel.organizationParam();
            
            UUID organizationId = extractOrganizationId(joinPoint, organizationParam);
            
            boolean hasAccess = checkAccessLevel(requiredLevel, organizationId);
            
            if (!hasAccess) {
                String message = StringUtils.hasText(requireAccessLevel.message()) 
                    ? requireAccessLevel.message()
                    : String.format("Access denied. Required access level: %s", requiredLevel.getValue());
                
                log.warn("Access level check failed for method {}: {}", joinPoint.getSignature().toShortString(), message);
                throw new AccessDeniedException(message);
            }
            
            log.debug("Access level check passed for method {} in {}ms", 
                     joinPoint.getSignature().toShortString(), System.currentTimeMillis() - startTime);
            
        } catch (AccessDeniedException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error during access level check for method {}", joinPoint.getSignature().toShortString(), e);
            throw new AccessDeniedException("Access level check failed due to system error");
        }
    }

    /**
     * Checks permissions based on the specified logic.
     * 
     * @param resource The resource type
     * @param actions The actions to check
     * @param logic The logic (ANY or ALL)
     * @param organizationId The organization ID (can be null)
     * @return true if access is granted, false otherwise
     */
    private boolean checkPermissions(String resource, String[] actions, RequirePermission.Logic logic, UUID organizationId) {
        if (actions == null || actions.length == 0) {
            return false;
        }
        
        if (logic == RequirePermission.Logic.ANY) {
            for (String action : actions) {
                if (rbacService.hasPermission(resource, action, organizationId)) {
                    return true;
                }
            }
            return false;
        } else {
            for (String action : actions) {
                if (!rbacService.hasPermission(resource, action, organizationId)) {
                    return false;
                }
            }
            return true;
        }
    }

    /**
     * Checks roles based on the specified logic.
     * 
     * @param roles The roles to check
     * @param logic The logic (ANY or ALL)
     * @param organizationId The organization ID (can be null)
     * @return true if access is granted, false otherwise
     */
    private boolean checkRoles(String[] roles, RequireRole.Logic logic, UUID organizationId) {
        if (roles == null || roles.length == 0) {
            return false;
        }
        
        Set<String> roleSet = Arrays.stream(roles).collect(Collectors.toSet());
        
        if (organizationId == null) {
            // Global role check - check if user has role in any organization
            for (String role : roles) {
                if (!rbacService.getOrganizationsWithRole(role).isEmpty()) {
                    if (logic == RequireRole.Logic.ANY) {
                        return true;
                    }
                } else if (logic == RequireRole.Logic.ALL) {
                    return false;
                }
            }
            return logic == RequireRole.Logic.ALL;
        } else {
            if (logic == RequireRole.Logic.ANY) {
                return rbacService.hasAnyRole(roleSet, organizationId);
            } else {
                return rbacService.hasAllRoles(roleSet, organizationId);
            }
        }
    }

    /**
     * Checks access level.
     * 
     * @param requiredLevel The required access level
     * @param organizationId The organization ID (can be null)
     * @return true if access is granted, false otherwise
     */
    private boolean checkAccessLevel(RoleBasedAccessControlService.AccessLevel requiredLevel, UUID organizationId) {
        if (organizationId == null) {
            return rbacService.hasAdministrativeAccess() && requiredLevel.isAtLeast(RoleBasedAccessControlService.AccessLevel.ADMIN);
        } else {
            RoleBasedAccessControlService.AccessLevel userLevel = rbacService.getAccessLevel(organizationId);
            return userLevel.isAtLeast(requiredLevel);
        }
    }

    /**
     * Extracts organization ID from method parameters.
     * 
     * @param joinPoint The method join point
     * @param organizationParam The parameter name containing organization ID
     * @return The organization ID or null if not found
     */
    private UUID extractOrganizationId(JoinPoint joinPoint, String organizationParam) {
        if (!StringUtils.hasText(organizationParam)) {
            return null;
        }
        
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Parameter[] parameters = method.getParameters();
            Object[] args = joinPoint.getArgs();
            
            for (int i = 0; i < parameters.length; i++) {
                if (organizationParam.equals(parameters[i].getName())) {
                    Object arg = args[i];
                    if (arg instanceof UUID) {
                        return (UUID) arg;
                    } else if (arg instanceof String) {
                        try {
                            return UUID.fromString((String) arg);
                        } catch (IllegalArgumentException e) {
                            log.warn("Invalid UUID format for organization parameter: {}", arg);
                        }
                    }
                    break;
                }
            }
        } catch (Exception e) {
            log.error("Error extracting organization ID from parameter {}", organizationParam, e);
        }
        
        return null;
    }
}
