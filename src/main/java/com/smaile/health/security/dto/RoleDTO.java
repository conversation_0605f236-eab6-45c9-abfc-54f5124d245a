package com.smaile.health.security.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * DTO for role information.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Role information")
public class RoleDTO {

    @Schema(description = "Role ID", example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @Schema(description = "Role name", example = "Insurance Company Administrator")
    private String name;

    @Schema(description = "Role code", example = "IC_ADMIN")
    private String code;

    @Schema(description = "Role description", example = "Administrator role for insurance company operations")
    private String description;

    @Schema(description = "Organization type", example = "IC")
    private String organizationType;

    @Schema(description = "Role scope", example = "ADMIN")
    private String scope;

    @Schema(description = "Hierarchy level", example = "0")
    private Integer hierarchyLevel;

    @Schema(description = "Whether this is a system role", example = "false")
    private boolean systemRole;

    @Schema(description = "Whether this role can be inherited", example = "true")
    private boolean inheritable;

    @Schema(description = "Role status", example = "ACTIVE")
    private String status;

    @Schema(description = "Number of permissions assigned to this role", example = "15")
    private int permissionCount;

    @Schema(description = "Number of users assigned to this role", example = "5")
    private int userCount;

    @Schema(description = "Date when role was created")
    private OffsetDateTime dateCreated;

    @Schema(description = "Date when role was last updated")
    private OffsetDateTime lastUpdated;
}
