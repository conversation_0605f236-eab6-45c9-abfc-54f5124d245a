package com.smaile.health.security.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for creating a new role.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request to create a new role")
public class CreateRoleRequestDTO {

    @Schema(description = "Role name", example = "Insurance Company Administrator", required = true)
    @NotBlank(message = "Role name is required")
    @Size(min = 3, max = 100, message = "Role name must be between 3 and 100 characters")
    private String name;

    @Schema(description = "Role code (unique identifier)", example = "IC_ADMIN", required = true)
    @NotBlank(message = "Role code is required")
    @Size(min = 2, max = 50, message = "Role code must be between 2 and 50 characters")
    @Pattern(regexp = "^[A-Z][A-Z0-9_]*$", message = "Role code must start with a letter and contain only uppercase letters, numbers, and underscores")
    private String code;

    @Schema(description = "Role description", example = "Administrator role for insurance company operations")
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;

    @Schema(description = "Organization type this role applies to", example = "IC", required = true)
    @NotBlank(message = "Organization type is required")
    @Pattern(regexp = "^(SUPER_SMAILE|IC|IC_TPA|IC_MP|IC_TPA_MP|SMAILE_TPA|SMAILE_MP|SMAILE_TPA_MP|PROFESSIONAL)$", 
             message = "Invalid organization type")
    private String organizationType;

    @Schema(description = "Role scope level", example = "ADMIN", required = true)
    @NotBlank(message = "Role scope is required")
    @Pattern(regexp = "^(ADMIN|MANAGER|STAFF)$", message = "Invalid role scope")
    private String scope;

    @Schema(description = "Whether this role can be inherited by child organizations", example = "true")
    @Builder.Default
    private boolean inheritable = true;
}
