package com.smaile.health.security.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * DTO for role assignment requests.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request to assign a role to a user")
public class RoleAssignmentRequestDTO {

    @Schema(description = "User ID", example = "123e4567-e89b-12d3-a456-************", required = true)
    @NotNull(message = "User ID is required")
    private UUID userId;

    @Schema(description = "Role ID", example = "123e4567-e89b-12d3-a456-************", required = true)
    @NotNull(message = "Role ID is required")
    private UUID roleId;

    @Schema(description = "Organization ID", example = "123e4567-e89b-12d3-a456-************", required = true)
    @NotNull(message = "Organization ID is required")
    private UUID organizationId;

    @Schema(description = "Date from which the role assignment is valid", example = "2025/08/26T00:00:00Z")
    private OffsetDateTime validFrom;

    @Schema(description = "Date until which the role assignment is valid", example = "2024-12-31T23:59:59Z")
    private OffsetDateTime validTo;

    @Schema(description = "Reason for role assignment", example = "Promoted to team lead position")
    @Size(max = 500, message = "Reason cannot exceed 500 characters")
    private String reason;

    @Schema(description = "Additional notes about the assignment", example = "Temporary assignment during manager's leave")
    @Size(max = 500, message = "Notes cannot exceed 500 characters")
    private String notes;
}
