package com.smaile.health.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrganizationType {
    SUPER_SMAILE("Super Smaile"),
    IC("Insurance Company"),
    IC_TPA("Insurance Company Third-Party Admin"),
    IC_MP("Insurance Company Medical Provider"),
    IC_TPA_MP("Insurance Company Third-Party Admin Medical Provider"),
    SMAILE_TPA("Smaile Third-Party Admin"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Smaile Medical Provider"),
    SMAILE_TPA_MP("Smaile Third-Party Admin Medical Provider"),
    PROFESSIONAL("Professional");

    private final String description;
}
