package com.smaile.health.constants;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 26/8/2025
 **/
public enum UserStatus {

    ACTIVE,
    INACTIVE,
    DELETED,
    SUSPENDED;

    private static final Map<String, UserStatus> mappings = new HashMap<>();

    static {
        for (UserStatus status : UserStatus.values()) {
            mappings.put(status.name(), status);
        }
    }

    @Nullable
    public static UserStatus resolve(@Nullable String status) {
        if (status == null) {
            return null;
        }
        return mappings.getOrDefault(status, null);
    }

    public boolean matches(String status) {
        return (this == resolve(status));
    }
}
