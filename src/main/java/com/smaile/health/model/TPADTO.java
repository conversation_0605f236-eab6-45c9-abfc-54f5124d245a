package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
@Schema(description = "Third Party Admin (TPA) Data Transfer Object")
public class TPADTO {

    @Schema(
            description = "Unique identifier of the TPA organization",
            example = "018f1234-5678-9abc-def0-123456789abc"
    )
    @JsonProperty("id")
    private UUID id;

    @Schema(
            description = "Name of the TPA organization",
            example = "ABC TPA Services",
            maxLength = 255
    )
    @Size(max = 255, message = "Name must not exceed 255 characters")
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "Internal code for the TPA organization",
            example = "TPA001",
            maxLength = 100
    )
    @Size(max = 100, message = "Code must not exceed 100 characters")
    @JsonProperty("code")
    private String code;

    @Schema(
            description = "Type of TPA organization",
            example = "IC_TPA",
            allowableValues = {"IC_TPA", "SMAILE_TPA"}
    )
    @JsonProperty("type")
    private OrganizationType type;

    @Schema(
            description = "Official registration number issued by regulatory authorities",
            example = "REG123456",
            maxLength = 100
    )
    @Size(max = 100, message = "Registration number must not exceed 100 characters")
    @JsonProperty("registration_number")
    private String registrationNumber;

    @Schema(
            description = "Current status of the TPA organization",
            example = "ACTIVE",
            allowableValues = {"ACTIVE", "DRAFT", "ARCHIVED"}
    )
    @JsonProperty("status")
    private OrganizationStatus status;

    @Schema(
            description = "Contact phone number for the TPA organization",
            example = "******-0123",
            maxLength = 20
    )
    @Size(max = 20, message = "Contact phone must not exceed 20 characters")
    @JsonProperty("contact_phone")
    private String contactPhone;

    @Schema(
            description = "Contact email address for the TPA organization",
            example = "<EMAIL>",
            maxLength = 255
    )
    @Email(message = "Contact email must be a valid email address")
    @Size(max = 255, message = "Contact email must not exceed 255 characters")
    @JsonProperty("contact_email")
    private String contactEmail;

    @Schema(
            description = "Physical address of the TPA organization",
            example = "123 Main St, New York, NY 10001",
            maxLength = 500
    )
    @Size(max = 500, message = "Address must not exceed 500 characters")
    @JsonProperty("address")
    private String address;

    @Schema(
            description = "Market/region where the TPA organization operates",
            example = "US",
            maxLength = 100
    )
    @Size(max = 100, message = "Market must not exceed 100 characters")
    @JsonProperty("market")
    private String market;

    @Schema(
            description = "Country where the TPA organization is registered",
            example = "United States",
            maxLength = 100
    )
    @Size(max = 100, message = "Country must not exceed 100 characters")
    @JsonProperty("country")
    private String country;

    @Schema(
            description = "Parent organization information",
            example = "{\"id\": \"018f1234-5678-9abc-def0-123456789abc\", \"name\": \"ABC Insurance Co.\"}"
    )
    @JsonProperty("parent")
    private ParentOrganizationDTO parent;

    @Schema(
            description = "Total number of admin users in this TPA organization",
            example = "5"
    )
    @JsonProperty("total_admin")
    private Integer totalAdmin;

    @Schema(
            description = "Timestamp when the TPA organization was created",
            example = "2024-01-15T10:30:00Z",
            readOnly = true
    )
    @JsonProperty("date_created")
    private OffsetDateTime dateCreated;

    @Schema(
            description = "User who created the TPA organization",
            example = "<EMAIL>",
            readOnly = true
    )
    @JsonProperty("created_by")
    private String createdBy;

    @Schema(
            description = "Timestamp when the TPA organization was last updated",
            example = "2024-01-15T10:30:00Z",
            readOnly = true
    )
    @JsonProperty("last_updated")
    private OffsetDateTime lastUpdated;

    @Schema(
            description = "User who last updated the TPA organization",
            example = "<EMAIL>",
            readOnly = true
    )
    @JsonProperty("updated_by")
    private String updatedBy;

    @Schema(
            description = "List of related organizations (child organizations)",
            example = "[]"
    )
    @JsonProperty("related_organizations")
    private List<OrganizationDTO> relatedOrganizations;

    @Schema(
            description = "List of users associated with this TPA organization (only populated for SUPER_SMAILE_ADMIN users)",
            example = "[]",
            readOnly = true
    )
    @JsonProperty("users")
    private List<UserDTO> users;
}
