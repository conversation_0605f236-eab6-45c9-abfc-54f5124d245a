package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "General Insurance Company Information Data Transfer Object")
public class InsuranceCompanyGeneralInfoDTO {

    @Schema(
            description = "Unique identifier of the insurance company",
            example = "018f1234-5678-9abc-def0-123456789abc",
            readOnly = true
    )
    @JsonProperty("id")
    private UUID id;

    @Schema(
            description = "Name of the insurance company",
            example = "ABC Insurance Co.",
            readOnly = true
    )
    @JsonProperty("name")
    private String name;
}
