package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
@Schema(description = "Parent Organization Data Transfer Object")
public class ParentOrganizationDTO {

    @Schema(
            description = "Unique identifier of the parent organization",
            example = "018f1234-5678-9abc-def0-123456789abc"
    )
    @JsonProperty("id")
    private UUID id;

    @Schema(
            description = "Name of the parent organization",
            example = "ABC Insurance Co."
    )
    @JsonProperty("name")
    private String name;
}
