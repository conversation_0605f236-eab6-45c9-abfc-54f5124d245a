package com.smaile.health.model.request;

import com.smaile.health.constants.SmaileConstant;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateOrgAdminRequest {
    @NotBlank
    @Size(max = 255)
    @Pattern(regexp = SmaileConstant.EMAIL_REGEX, message = "Invalid email format")
    private String email;

    @NotBlank
    @Size(max = 255)
    private String username;

    @NotBlank
    @Size(max = 255)
    private String fullName;

    @Size(max = 20)
    private String phone;

    @NotBlank
    @Size(max = 20)
    private String status;

}
