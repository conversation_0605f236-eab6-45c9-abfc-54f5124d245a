package com.smaile.health.model.request;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.SmaileConstant;
import com.smaile.health.model.MedicalProviderLicense;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.UUID;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateMedicalProviderRequest {

    private String name;

    private String code;

    private OrganizationType type;

    private String providerType;

    private String registrationNumber;

    private String market;

    private OrganizationStatus status;

    private String contactPhone;

    @NotBlank
    @Size(max = 255)
    @Pattern(regexp = SmaileConstant.EMAIL_REGEX, message = "Invalid email format")
    private String contactEmail;

    private String address;

    @NotNull
    private UUID parentId;

    @NotEmpty
    private List<String> specialities;

    private List<MedicalProviderLicense> licenses;

    @NotNull
    @Valid
    private CreateOrgAdminRequest adminUserRequest;
}
