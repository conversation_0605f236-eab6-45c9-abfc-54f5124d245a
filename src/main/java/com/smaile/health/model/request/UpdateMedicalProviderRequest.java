package com.smaile.health.model.request;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.SmaileConstant;
import com.smaile.health.model.MedicalProviderLicense;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.UUID;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateMedicalProviderRequest {
    @NotNull
    private UUID id;

    private String name;

    private String code;


    private String legalId;

    private String market;

    private String registrationNumber;

    @NotNull
    private OrganizationStatus status;

    private String contactPhone;

    @Pattern(regexp = SmaileConstant.EMAIL_REGEX)
    private String contactEmail;

    private String address;
    private List<String> specialities;
    private List<MedicalProviderLicense> licenses;
}
