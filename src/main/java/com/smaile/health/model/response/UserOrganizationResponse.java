package com.smaile.health.model.response;

import com.smaile.health.constants.OrganizationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.UUID;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserOrganizationResponse {
    private UUID id;
    private String name;
    private OrganizationType type;
}
