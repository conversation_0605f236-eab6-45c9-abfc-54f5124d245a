# Error message
error.permission-denied=You don't have permission to perform this action
error.not-implemented=The function has not been implemented yet

# Insurance Company errors
error.insurance-company.not-found=Insurance Company not found
error.insurance-company.name-duplicate=Insurance Company with this name already exists
# TPA errors
error.tpa.not-found=TPA organization not found
error.tpa.name-duplicate=TPA organization with this name already exists
error.tpa.insufficient-permissions-to-create=You don't have permission to create TPA organizations. Only IC and SUPER_SMAILE organizations can create TPAs.

# Organization errors
error.organization.not-found=Organization not found
error.organization.name-duplicate=Organization with this name already exists
error.user-organization.not-found=User organization not found
# User errors
error.user.not-found=User not found
error.user.is-deleted=User is deleted and cannot be used
error.user.email-duplicate=User with this email already exists
# Role errors
error.role.not-found=Role not found

# General errors
error.validation=Validation error occurred
error.internal-server=Internal server error occurred

# Message
msg.professional.duplicate-license-id=Professional with this license ID {0} already exists
msg.professional.invalid-status=Professional with Invalid status to this action
msg.sample-with-param=Hello {0}
msg.insurance-company.created=Insurance Company created successfully
msg.insurance-company.updated=Insurance Company updated successfully
msg.insurance-company.approved=Insurance Company approved successfully
msg.insurance-company.deleted=Insurance Company deleted successfully
msg.insurance-company.archived=Insurance Company archived successfully
msg.insurance-company.operation.failed=Insurance Company operation failed
msg.user.operation.failed=User operation failed
msg.insurance-company.cannot-update-archived=Cannot update archived insurance company
msg.insurance-company.already-archived=Insurance Company is already archived
msg.insurance-company.already-approved=Insurance Company is already approved
msg.organization.updated=Organization updated successfully
msg.organization.created=Organization created successfully
msg.organization.retrieved=Organization retrieved successfully
msg.organization.deleted=Organization deleted successfully
msg.insurance-company.retrieved=Insurance Company retrieved successfully
# TPA messages
msg.tpa.created=TPA organization created successfully
msg.tpa.updated=TPA organization updated successfully
msg.tpa.retrieved=TPA organization retrieved successfully
msg.tpa.archived=TPA organization archived successfully
msg.tpa.operation.failed=TPA organization operation failed
msg.tpa.cannot-update-archived=Cannot update archived TPA organization
msg.tpa.already-archived=TPA organization is already archived
