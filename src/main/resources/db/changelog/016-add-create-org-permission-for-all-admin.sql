--liquibase formatted sql
--changeset longnh:016-add-create-org-permission-for-all-admin
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('dea40608-d863-4b32-860d-19c4385d89d9'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa26'::uuid, '9c24774f-1104-4a4b-9e9e-24e90eee4c5c'::uuid, NULL, '2025-08-19 19:21:10.068 +0700', '2025-08-19 19:21:10.068 +0700', NULL, NULL) ON CONFLICT DO NOTHING;
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('33c0d9c2-8700-407a-9ff6-d8bbfe0f9687'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa26'::uuid, '8c627d05-a297-432f-8e8f-9fd2dd950ca5'::uuid, NULL, '2025-08-19 19:21:10.068 +0700', '2025-08-19 19:21:10.068 +0700', NULL, NULL) ON CONFLICT DO NOTHING;
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('d13337e1-c318-4f0b-9115-22914b5ccf77'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa26'::uuid, '0eab93ed-c22a-4f77-8101-aa329e27a0e5'::uuid, NULL, '2025-08-19 19:21:10.068 +0700', '2025-08-19 19:21:10.068 +0700', NULL, NULL) ON CONFLICT DO NOTHING;
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('789eb991-8d29-4508-a000-d6599f2fe97a'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa26'::uuid, '6d0465a7-59fa-43e4-a1ae-b787b333365a'::uuid, NULL, '2025-08-19 19:21:10.068 +0700', '2025-08-19 19:21:10.068 +0700', NULL, NULL) ON CONFLICT DO NOTHING;
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('46597dc9-ec90-4ad5-92a8-32ce5e41564d'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa46'::uuid, '9c24774f-1104-4a4b-9e9e-24e90eee4c5c'::uuid, NULL, '2025-08-19 19:21:19.740 +0700', '2025-08-19 19:21:19.740 +0700', NULL, NULL) ON CONFLICT DO NOTHING;
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('f8c9633b-69e4-414a-9205-a5f5f108223a'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa46'::uuid, '8c627d05-a297-432f-8e8f-9fd2dd950ca5'::uuid, NULL, '2025-08-19 19:21:19.740 +0700', '2025-08-19 19:21:19.740 +0700', NULL, NULL) ON CONFLICT DO NOTHING;
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('1b84b8b6-6980-4cf4-b564-4d1b0219f515'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa46'::uuid, '0eab93ed-c22a-4f77-8101-aa329e27a0e5'::uuid, NULL, '2025-08-19 19:21:19.740 +0700', '2025-08-19 19:21:19.740 +0700', NULL, NULL) ON CONFLICT DO NOTHING;
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('dbd728ed-7991-4ded-99b6-7e4850592765'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa46'::uuid, '6d0465a7-59fa-43e4-a1ae-b787b333365a'::uuid, NULL, '2025-08-19 19:21:19.740 +0700', '2025-08-19 19:21:19.740 +0700', NULL, NULL) ON CONFLICT DO NOTHING;
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('bba5a2fb-10fd-46df-ac63-c2f2e314da5e'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa36'::uuid, '9c24774f-1104-4a4b-9e9e-24e90eee4c5c'::uuid, NULL, '2025-08-19 19:21:30.752 +0700', '2025-08-19 19:21:30.752 +0700', NULL, NULL) ON CONFLICT DO NOTHING;
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('fb5d77b4-543b-48b6-9bf2-bd0705a080cf'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa36'::uuid, '8c627d05-a297-432f-8e8f-9fd2dd950ca5'::uuid, NULL, '2025-08-19 19:21:30.752 +0700', '2025-08-19 19:21:30.752 +0700', NULL, NULL) ON CONFLICT DO NOTHING;
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('25ecd9e6-7e4a-4750-81f1-a3fa48b67680'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa36'::uuid, '0eab93ed-c22a-4f77-8101-aa329e27a0e5'::uuid, NULL, '2025-08-19 19:21:30.752 +0700', '2025-08-19 19:21:30.752 +0700', NULL, NULL) ON CONFLICT DO NOTHING;
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('8e0f3d83-6586-4616-816f-65fea7971419'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa36'::uuid, '6d0465a7-59fa-43e4-a1ae-b787b333365a'::uuid, NULL, '2025-08-19 19:21:30.752 +0700', '2025-08-19 19:21:30.752 +0700', NULL, NULL) ON CONFLICT DO NOTHING;

--rollback DELETE FROM public.roles_permissions WHERE id='dea40608-d863-4b32-860d-19c4385d89d9'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='33c0d9c2-8700-407a-9ff6-d8bbfe0f9687'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='d13337e1-c318-4f0b-9115-22914b5ccf77'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='789eb991-8d29-4508-a000-d6599f2fe97a'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='46597dc9-ec90-4ad5-92a8-32ce5e41564d'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='f8c9633b-69e4-414a-9205-a5f5f108223a'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='1b84b8b6-6980-4cf4-b564-4d1b0219f515'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='dbd728ed-7991-4ded-99b6-7e4850592765'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='bba5a2fb-10fd-46df-ac63-c2f2e314da5e'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='fb5d77b4-543b-48b6-9bf2-bd0705a080cf'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='25ecd9e6-7e4a-4750-81f1-a3fa48b67680'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='8e0f3d83-6586-4616-816f-65fea7971419'::uuid;