--liquibase formatted sql

--changeset toanpham:010-add-tpa-organizations-table
-- Create tpa_organizations table (extends organizations with JOINED strategy)
-- This table contains only TPA specific fields and references organizations table
CREATE TABLE tpa_organizations
(
    id      UUID         NOT NULL,
    market  VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (id) REFERENCES organizations (id) ON DELETE CASCADE
);

--rollback DROP TABLE tpa_organizations;
