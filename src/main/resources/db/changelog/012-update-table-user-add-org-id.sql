--liquibase formatted sql
--changeset longnh:012-update-table-user-add-org-id

ALTER TABLE public.users ADD organization_id uuid NULL;
ALTER TABLE public.users ADD role_id uuid NULL;
ALTER TABLE public.users ADD CONSTRAINT users_roles_fk FOREIGN KEY (role_id) REFERENCES public.roles(id);
ALTER TABLE public.users ADD CONSTRAINT users_organizations_fk FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--rollback ALTER TABLE public.users DROP CONSTRAINT users_organizations_fk;
--rollback ALTER TABLE public.users DROP CONSTRAINT users_roles_fk;
--rollback ALTER TABLE public.users DROP COLUMN organization_id;
--rollback ALTER TABLE public.users DROP COLUMN role_id;
