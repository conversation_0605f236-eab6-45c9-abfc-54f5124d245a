--liquibase formatted sql

--changeset maiph:014-add-permits-professional-resource
INSERT INTO public.permissions
(id, "name", resource, sub_resource, "action", description, "version", date_created, last_updated, created_by, updated_by)
VALUES
('0198cbd4-18ef-7c92-b64e-abf6129e63db'::uuid, 'READ_PROFESSIONAL', 'professionals', '*', 'read', 'Read professional', 0, '2025-08-18 03:18:02.677', '2025-08-18 03:18:02.677', NULL, NULL);

INSERT INTO public.permissions
(id, "name", resource, sub_resource, "action", description, "version", date_created, last_updated, created_by, updated_by)
VALUES
('0198cbd3-f53f-73b3-b7b3-8fe624aa3318'::uuid, 'UPDATE_PROFESSIONAL', 'professionals', '*', 'update', 'Update professional', 0, '2025-08-18 03:18:02.677', '2025-08-18 03:18:02.677', NULL, NULL);

--changeset maiph:014-1-add-permits-professional-resource
INSERT INTO public.roles_permissions
(id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by)
VALUES
('0198cbdc-13b0-750d-b08f-ef6cae266b9d'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa16'::uuid, '0198cbd3-f53f-73b3-b7b3-8fe624aa3318'::uuid, 0, '2025-08-15 02:50:31.228', '2025-08-15 02:50:31.228', NULL, NULL);

INSERT INTO public.roles_permissions
(id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by)
VALUES
('0198cbdc-3779-71eb-bc3f-beb734bdc0b7'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa16'::uuid, '0198cbd4-18ef-7c92-b64e-abf6129e63db'::uuid, 0, '2025-08-15 02:50:31.228', '2025-08-15 02:50:31.228', NULL, NULL);
