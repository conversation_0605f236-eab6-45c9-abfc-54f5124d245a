apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: {{ include "chart.name" . }}-gateway
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - {{ .Values.global.subDomain }}.{{ .Values.global.baseDomain }}
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: smaile-api-tls
    hosts:
    - {{ .Values.global.subDomain }}.{{ .Values.global.baseDomain }}
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: {{ include "chart.name" . }}
spec:
  gateways:
  - {{ .Chart.Name }}-gateway
  hosts:
  - {{ .Values.global.subDomain }}.{{ .Values.global.baseDomain }}
  http:
  - match:
    - port: 80
      uri:
        prefix: "/"
    redirect:
      scheme: https
      redirectCode: 301
    headers:
      response:
        set:
          Access-Control-Allow-Headers: '*'
          Access-Control-Allow-Origin: '*'
  - match:
    - port: 443
      uri:
        prefix: "/api/"
    route:
    - destination:
        host: {{ .Chart.Name }}.{{ .Release.Namespace }}.{{ .Values.clusterDomain }}
        port:
          number: 80
      headers:
        response:
          set:
            Access-Control-Allow-Headers: '*'
            Access-Control-Allow-Origin: '*'
    corsPolicy:
{{- if contains "dev" .Values.global.baseDomain }}
{{ .Values.cors.dev | toYaml | indent 6 }}
{{- else }}
{{ .Values.cors.prod | toYaml | indent 6 }}
{{- end }}
---
apiVersion: security.istio.io/v1beta1
kind: RequestAuthentication
metadata:
  name: {{ include "chart.name" . }}-request-authentication
spec:
  selector:
    matchLabels:
      app: {{ include "chart.name" . }}
  jwtRules:
    - issuer: "https://iam.{{ .Values.global.baseDomain }}/realms/{{ .Values.application.iam.realm }}"
      jwksUri: "https://iam.{{ .Values.global.baseDomain }}/realms/{{ .Values.application.iam.realm }}/protocol/openid-connect/certs"
      forwardOriginalToken: true
      outputClaimToHeaders:
        - header: "{{ .Values.keycloakUserId.header }}"
          claim: "{{ .Values.keycloakUserId.claim }}"
---
# Allow swagger-ui without JWT
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: {{ include "chart.name" . }}-swagger-allow
spec:
  selector:
    matchLabels:
      app: {{ include "chart.name" . }}
  action: ALLOW
  rules:
    - to:
        - operation:
            paths: ["/api/v1/swagger-ui*", "/api/v1/v3/api-docs*", "/api/v1/register/*", "/api/v1/utilities/*"]

---
# Require JWT for all other API v1 paths
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: {{ include "chart.name" . }}-auth
spec:
  selector:
    matchLabels:
      app: {{ include "chart.name" . }}
  action: ALLOW
  rules:
    - when:
        - key: request.auth.claims[iss]
          values: ["https://iam.{{ .Values.global.baseDomain }}/realms/{{ .Values.application.iam.realm }}"]
      to:
        - operation:
            paths: ["/api/v1/*"]